import { agentPlaygroundFlagFrontend } from '@/flags';
import { isFlagEnabled } from '@/lib/feature-flags';
import { Metadata } from 'next';
import { redirect } from 'next/navigation';

export const metadata: Metadata = {
  title: 'Agent Conversation | Orchestra Connect',
  description: 'Interactive agent conversation powered by Orchestra Connect',
  openGraph: {
    title: 'Agent Conversation | Orchestra Connect',
    description: 'Interactive agent conversation powered by Orchestra Connect',
    type: 'website',
  },
};

export default async function AgentsLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return <>{children}</>;
}
