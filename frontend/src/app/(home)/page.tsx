'use client';

import { useEffect, useState } from 'react';
import { CTASection } from '@/components/home/<USER>/cta-section';
// import { FAQSection } from "@/components/sections/faq-section";
import { FooterSection } from '@/components/home/<USER>/footer-section';
import { HeroSection } from '@/components/home/<USER>/hero-section';
import { OpenSourceSection } from '@/components/home/<USER>/open-source-section';
import { PricingSection } from '@/components/home/<USER>/pricing-section';
import { UseCasesSection } from '@/components/home/<USER>/use-cases-section';
import { ModalProviders } from '@/providers/modal-providers';
import { HeroVideoSection } from '@/components/home/<USER>/hero-video-section';
import { BackgroundAALChecker } from '@/components/auth/background-aal-checker';
import AuroraBackground from '@/components/home/<USER>/AuroraBackground';
import { ProcessSection } from '@/components/home/<USER>/process-section';

export default function Home() {
  return (
    <>
      <AuroraBackground />
      <BackgroundAALChecker>
        <main className="flex flex-col items-center justify-center min-h-screen w-full">
          <div className="w-full divide-y divide-border">
            <HeroSection />
            <UseCasesSection />
            {/* <CompanyShowcase /> */}
            {/* <BentoSection /> */}
            {/* <QuoteSection /> */}
            {/* <FeatureSection /> */}
            {/* <GrowthSection /> */}
            <ProcessSection />
            <div className='flex flex-col items-center px-4'>
              <PricingSection />
            </div>
            {/* <TestimonialSection /> */}
            {/* <FAQSection /> */}
            <CTASection />
            <FooterSection />
          </div>
        </main>
      </BackgroundAALChecker>
    </>
  );
}
