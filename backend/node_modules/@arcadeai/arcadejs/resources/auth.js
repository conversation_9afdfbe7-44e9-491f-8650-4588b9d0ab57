"use strict";
// File generated from our OpenAPI spec by Stainless. See CONTRIBUTING.md for details.
Object.defineProperty(exports, "__esModule", { value: true });
exports.Auth = exports.AuthorizationError = exports.DEFAULT_LONGPOLL_WAIT_TIME = void 0;
const resource_1 = require("../resource.js");
exports.DEFAULT_LONGPOLL_WAIT_TIME = 45;
/**
 * Error thrown when authorization-related operations fail
 */
class AuthorizationError extends Error {
    constructor(message) {
        super(message);
        this.name = 'AuthorizationError';
    }
}
exports.AuthorizationError = AuthorizationError;
class Auth extends resource_1.APIResource {
    /**
     * Starts the authorization process for a given provider and scopes.
     * @param userId - The user ID for which authorization is being requested
     * @param provider - The authorization provider (e.g., 'github', 'google', 'linkedin', 'microsoft', 'slack', 'spotify', 'x', 'zoom')
     * @param options - Optional parameters
     * @param options.providerType - The type of authorization provider. Defaults to 'oauth2'
     * @param options.scopes - A list of scopes required for authorization, if any. Defaults to []
     * @returns The authorization response
     *
     * Example:
     * ```ts
     * const authResponse = await client.auth.start("<EMAIL>", "github");
     * ```
     */
    start(userId, provider, options = {}) {
        const { providerType = 'oauth2', scopes = [] } = options;
        const authRequirement = {
            provider_id: provider,
            provider_type: providerType,
            oauth2: {
                scopes,
            },
        };
        return this.authorize({
            auth_requirement: authRequirement,
            user_id: userId,
        });
    }
    /**
     * Starts the authorization process for given authorization requirements
     */
    authorize(body, options) {
        return this._client.post('/v1/auth/authorize', { body, ...options });
    }
    /**
     * Confirms a user's details during an authorization flow
     */
    confirmUser(body, options) {
        return this._client.post('/v1/auth/confirm_user', { body, ...options });
    }
    /**
     * Checks the status of an ongoing authorization process for a specific tool. If
     * 'wait' param is present, does not respond until either the auth status becomes
     * completed or the timeout is reached.
     */
    status(query, options) {
        return this._client.get('/v1/auth/status', { query, ...options });
    }
    /**
     * Waits for the authorization process to complete.
     * @param authResponseOrId - The authorization response or ID to wait for completion
     * @returns The completed authorization response
     * @throws {AuthorizationError} When the authorization ID is missing or invalid
     *
     * Example:
     * ```ts
     * const authResponse = await client.auth.start("<EMAIL>", "github");
     * try {
     *   const completedAuth = await client.auth.waitForCompletion(authResponse);
     *   console.log('Authorization completed:', completedAuth);
     * } catch (error) {
     *   if (error instanceof AuthorizationError) {
     *     console.error('Authorization failed:', error.message);
     *   }
     * }
     * ```
     */
    async waitForCompletion(authResponseOrId) {
        let authId;
        let authResponse;
        if (typeof authResponseOrId === 'string') {
            authId = authResponseOrId;
            authResponse = { status: 'pending' };
        }
        else {
            if (!authResponseOrId.id) {
                throw new AuthorizationError('Authorization ID is required');
            }
            authId = authResponseOrId.id;
            authResponse = authResponseOrId;
        }
        while (authResponse.status !== 'completed') {
            authResponse = await this.status({
                id: authId,
                wait: exports.DEFAULT_LONGPOLL_WAIT_TIME,
            });
        }
        return authResponse;
    }
}
exports.Auth = Auth;
//# sourceMappingURL=auth.js.map