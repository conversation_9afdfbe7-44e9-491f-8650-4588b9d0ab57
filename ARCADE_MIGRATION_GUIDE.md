# Guide de Migration Pipedream → Arcade.dev

Ce guide détaille la migration complète de l'intégration Pipedream vers Arcade.dev dans Kortix Suna.

## 📋 Vue d'Ensemble

La migration remplace l'intégration Pipedream existante par Arcade.dev tout en maintenant la compatibilité et les fonctionnalités existantes.

### Avantages de la Migration

- **Réduction des coûts** : 60-75% d'économies sur les frais d'intégration
- **Flexibilité accrue** : Outils personnalisés avec TDK
- **Sécurité améliorée** : Authentification par utilisateur
- **Architecture moderne** : Support MCP natif, SDK modernes

## 🚀 Processus de Migration

### Phase 1 : Préparation

1. **Configuration des Variables d'Environnement**
   ```bash
   # Ajouter dans .env
   ARCADE_API_KEY="your_arcade_api_key"
   ENABLE_ARCADE_INTEGRATION="false"  # Commencer par false
   INTEGRATION_MIGRATION_MODE="feature_flag"
   ```

2. **Installation des Dépendances**
   ```bash
   # Backend
   cd backend
   pip install arcadepy

   # Frontend
   cd frontend
   npm install @arcadeai/arcadejs
   ```

3. **Création de la Table Arcade**
   ```sql
   -- Exécuter la migration SQL
   psql -d your_database -f backend/arcade/migrations/001_create_arcade_profiles.sql
   ```

### Phase 2 : Test de Connectivité

1. **Test de l'API Arcade**
   ```bash
   cd backend
   python test_arcade_connection.py
   ```

2. **Vérification des Endpoints**
   ```bash
   curl http://localhost:8000/arcade/health
   curl http://localhost:8000/integration/status
   ```

### Phase 3 : Migration des Données

1. **Dry Run (Recommandé)**
   ```bash
   cd backend
   python scripts/migrate_pipedream_to_arcade.py --dry-run
   ```

2. **Migration Réelle**
   ```bash
   # ⚠️ ATTENTION : Faire une sauvegarde avant !
   python scripts/migrate_pipedream_to_arcade.py --execute
   ```

### Phase 4 : Activation Progressive

#### Option A : Feature Flag Global
```bash
# Activer Arcade pour tous les utilisateurs
export ENABLE_ARCADE_INTEGRATION="true"
```

#### Option B : Migration par Utilisateur
```bash
# Changer le mode de migration
export INTEGRATION_MIGRATION_MODE="user_choice"

# Migrer des utilisateurs spécifiques via l'API
curl -X POST http://localhost:8000/integration/switch \
  -H "Content-Type: application/json" \
  -d '{"provider": "arcade", "user_id": "user123"}'
```

## 🔧 Configuration Détaillée

### Variables d'Environnement

| Variable | Description | Valeur par Défaut |
|----------|-------------|-------------------|
| `ARCADE_API_KEY` | Clé API Arcade.dev | Requis |
| `ARCADE_ENGINE_URL` | URL du moteur Arcade (optionnel) | - |
| `ENABLE_ARCADE_INTEGRATION` | Active l'intégration Arcade | `false` |
| `INTEGRATION_MIGRATION_MODE` | Mode de migration | `feature_flag` |

### Modes de Migration

1. **`feature_flag`** : Tous les utilisateurs utilisent le même provider
2. **`user_choice`** : Chaque utilisateur peut choisir son provider

### Mapping des Applications

| Pipedream App | Arcade Toolkit | Notes |
|---------------|----------------|-------|
| `gmail` | `gmail` | Compatible direct |
| `slack` | `slack` | Compatible direct |
| `github` | `github` | Compatible direct |
| `notion` | `notion` | Compatible direct |
| `google-drive` | `google-drive` | Compatible direct |
| `google-calendar` | `calendar` | Nom différent |
| `hubspot` | `hubspot` | Compatible direct |

## 📊 Monitoring et Statistiques

### Endpoints de Monitoring

- `GET /integration/status` - Statut de l'intégration
- `GET /integration/migration-stats` - Statistiques de migration
- `GET /integration/health` - Santé des intégrations
- `GET /arcade/health` - Santé spécifique Arcade

### Métriques Importantes

- Nombre d'utilisateurs migrés
- Taux de réussite des connexions
- Performance des outils Arcade vs Pipedream
- Erreurs et échecs de migration

## 🔄 Rollback

En cas de problème, vous pouvez revenir à Pipedream :

```bash
# Désactiver Arcade
export ENABLE_ARCADE_INTEGRATION="false"

# Ou via l'API
curl -X POST http://localhost:8000/integration/switch \
  -H "Content-Type: application/json" \
  -d '{"provider": "pipedream"}'
```

## 🛠️ Dépannage

### Problèmes Courants

1. **Erreur de connexion Arcade**
   - Vérifier `ARCADE_API_KEY`
   - Tester avec `python test_arcade_connection.py`

2. **Outils manquants après migration**
   - Vérifier le mapping des toolkits
   - Reconfigurer les outils activés dans les profils

3. **Erreurs MCP**
   - Vérifier les configurations MCP dans les agents
   - Redémarrer les sessions MCP

### Logs Utiles

```bash
# Logs de migration
tail -f logs/migration.log

# Logs d'intégration
tail -f logs/integration.log

# Logs Arcade
tail -f logs/arcade.log
```

## 📞 Support

En cas de problème :

1. Consulter les logs détaillés
2. Vérifier les statistiques de migration
3. Tester les connexions individuellement
4. Utiliser le mode dry-run pour diagnostiquer

## ✅ Checklist de Migration

- [ ] Variables d'environnement configurées
- [ ] Dépendances installées
- [ ] Table `arcade_profiles` créée
- [ ] Test de connectivité Arcade réussi
- [ ] Sauvegarde de la base de données effectuée
- [ ] Migration en dry-run testée
- [ ] Migration réelle exécutée
- [ ] Tests de fonctionnalité post-migration
- [ ] Monitoring activé
- [ ] Documentation utilisateur mise à jour

## 🎯 Prochaines Étapes

Après la migration réussie :

1. **Optimisation** : Configurer les outils spécifiques par utilisateur
2. **Formation** : Former les utilisateurs aux nouvelles fonctionnalités
3. **Monitoring** : Surveiller les performances et l'utilisation
4. **Nettoyage** : Supprimer les anciennes configurations Pipedream (après validation)

---

**Note** : Cette migration est conçue pour être progressive et réversible. Prenez le temps de tester chaque étape avant de passer à la suivante.
