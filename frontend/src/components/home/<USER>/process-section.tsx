import { SectionHeader } from '@/components/home/<USER>';
import Link from 'next/link';

export function ProcessSection() {
  return (
    <section
      id="process"
      className="flex flex-col items-center justify-center w-full relative pb-18"
    >
      <div className="w-full max-w-6xl mx-auto px-6">
        <SectionHeader>
          <h2 className="text-3xl md:text-4xl font-medium tracking-tighter text-center text-balance pb-1">
            Comment fonctionne Orchestra Connect ?
          </h2>
          <p className="text-muted-foreground text-center text-balance font-medium">
            Notre approche s'appuie sur les meilleures technologies pour
            garantir fiabilité, simplicité et efficacité.
          </p>
        </SectionHeader>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 pt-12">
          {/* Étape 1 */}
          <div className="rounded-xl bg-[#F3F4F6] dark:bg-[#F9FAFB]/[0.02] border border-border p-6 backdrop-blur-md bg-accent/70 hover:bg-accent/80 hover:shadow-[0_0_15px_rgba(0,214,143,0.15)] transition-all">
            <div className="flex flex-col gap-4">
              <div className="flex items-center gap-3">
                <div className="rounded-full bg-primary/10 p-2 size-10 flex items-center justify-center">
                  <span className="text-primary font-bold text-lg">1</span>
                </div>
                <h3 className="text-xl font-medium line-clamp-1">
                  Exprimez vos besoins
                </h3>
              </div>
              <p className="text-muted-foreground">
                Décrivez simplement votre demande à Alex, notre agent IA, comme
                vous le feriez avec un collègue. Utilisez votre langage naturel,
                sans besoin de mots-clés spécifiques.
              </p>
              <div className="mt-4 rounded-lg overflow-hidden border border-border flex items-center justify-center">
                <img
                  src="https://images.unsplash.com/photo-1552664730-d307ca884978?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2069&q=80"
                  alt="Expression des besoins"
                  className="w-full h-40 object-cover object-center"
                  onError={(e) => {
                    e.currentTarget.src = `https://placehold.co/800x400/f5f5f5/666666?text=Étape+1:+Expression+des+besoins`;
                  }}
                />
              </div>
            </div>
          </div>

          {/* Étape 2 */}
          <div className="rounded-xl bg-[#F3F4F6] dark:bg-[#F9FAFB]/[0.02] border border-border p-6 backdrop-blur-md bg-accent/70 hover:bg-accent/80 hover:shadow-[0_0_15px_rgba(0,214,143,0.15)] transition-all">
            <div className="flex flex-col gap-4">
              <div className="flex items-center gap-3">
                <div className="rounded-full bg-primary/10 p-2 size-10 flex items-center justify-center">
                  <span className="text-primary font-bold text-lg">2</span>
                </div>
                <h3 className="text-xl font-medium line-clamp-1">
                  Alex analyse et traite
                </h3>
              </div>
              <p className="text-muted-foreground">
                Notre IA comprend le contexte de votre demande, consulte vos
                données et documents, et utilise son intelligence pour préparer
                une réponse personnalisée et pertinente.
              </p>
              <div className="mt-4 rounded-lg overflow-hidden border border-border flex items-center justify-center">
                <img
                  src="https://images.unsplash.com/photo-1518186285589-2f7649de83e0?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2074&q=80"
                  alt="Analyse et traitement"
                  className="w-full h-40 object-cover object-center"
                  onError={(e) => {
                    e.currentTarget.src = `https://placehold.co/800x400/f5f5f5/666666?text=Étape+2:+Analyse+et+traitement`;
                  }}
                />
              </div>
            </div>
          </div>

          {/* Étape 3 */}
          <div className="rounded-xl bg-[#F3F4F6] dark:bg-[#F9FAFB]/[0.02] border border-border p-6 backdrop-blur-md bg-accent/70 hover:bg-accent/80 hover:shadow-[0_0_15px_rgba(0,214,143,0.15)] transition-all">
            <div className="flex flex-col gap-4">
              <div className="flex items-center gap-3">
                <div className="rounded-full bg-primary/10 p-2 size-10 flex items-center justify-center">
                  <span className="text-primary font-bold text-lg">3</span>
                </div>
                <h3 className="text-xl font-medium line-clamp-1">
                  Résultats concrets
                </h3>
              </div>
              <p className="text-muted-foreground">
                Obtenez rapidement des résultats exploitables : rapports,
                analyses, synthèses ou actions concrètes. Alex peut même
                exécuter directement certaines tâches pour vous faire gagner du
                temps.
              </p>
              <div className="mt-4 rounded-lg overflow-hidden border border-border flex items-center justify-center">
                <img
                  src="https://images.unsplash.com/photo-1460925895917-afdab827c52f?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2015&q=80"
                  alt="Résultats concrets"
                  className="w-full h-40 object-cover object-center"
                  onError={(e) => {
                    e.currentTarget.src = `https://placehold.co/800x400/f5f5f5/666666?text=Étape+3:+Résultats+concrets`;
                  }}
                />
              </div>
            </div>
          </div>
        </div>

        <div className="mt-12 text-center">
          <Link
            href="#pricing"
            className="group inline-flex h-10 items-center justify-center gap-2 text-sm font-medium tracking-wide rounded-full text-primary-foreground dark:text-black px-6 shadow-[inset_0_1px_2px_rgba(255,255,255,0.25),0_3px_3px_-1.5px_rgba(16,24,40,0.06),0_1px_1px_rgba(16,24,40,0.08)] bg-primary dark:bg-white hover:bg-primary/90 dark:hover:bg-white/90 transition-all duration-200 w-fit mx-auto"
          >
            <span>Essayer Orchestra Connect</span>
            <span className="inline-flex items-center justify-center size-5 rounded-full bg-white/20 dark:bg-black/10 group-hover:bg-white/30 dark:group-hover:bg-black/20 transition-colors duration-200">
              <svg
                width="12"
                height="12"
                viewBox="0 0 24 24"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
                className="text-primary-foreground dark:text-black"
              >
                <path
                  d="M7 17L17 7M17 7H8M17 7V16"
                  stroke="currentColor"
                  strokeWidth="2"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                />
              </svg>
            </span>
          </Link>
        </div>
      </div>
    </section>
  );
}
