'use client';

import { MoreHorizontal } from 'lucide-react';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '../ui/dropdown-menu';
import { Button } from '../ui/button';
import { GetAccountMembersResponse } from '@usebasejump/shared';
import { useEffect, useState } from 'react';
import {
  DialogHeader,
  Dialog,
  DialogContent,
  DialogTitle,
  DialogDescription,
} from '@/components/ui/dialog';
import EditTeamMemberRoleForm from './edit-team-member-role-form';
import DeleteTeamMemberForm from './delete-team-member-form';

type Props = {
  accountId: string;
  teamMember: GetAccountMembersResponse[0];
  isPrimaryOwner: boolean;
};

export default function TeamMemberOptions({
  teamMember,
  accountId,
  isPrimaryOwner,
}: Props) {
  const [updateTeamRole, toggleUpdateTeamRole] = useState(false);
  const [removeTeamMember, toggleRemoveTeamMember] = useState(false);

  useEffect(() => {
    if (updateTeamRole) {
      toggleUpdateTeamRole(false);
    }
  }, [teamMember.account_role, updateTeamRole]);

  return (
    <>
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button
            variant="ghost"
            className="h-8 w-8 p-0 rounded-full hover:bg-hover-bg dark:hover:bg-hover-bg-dark"
          >
            <MoreHorizontal className="h-4 w-4 text-foreground/70" />
            <span className="sr-only">Ouvrir le menu</span>
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent className="min-w-[160px] border-subtle dark:border-white/10 bg-card-bg dark:bg-background-secondary rounded-xl shadow-custom">
          <DropdownMenuItem
            onSelect={() => toggleUpdateTeamRole(true)}
            className="rounded-md hover:bg-hover-bg cursor-pointer text-foreground/90"
          >
            Changer de rôle
          </DropdownMenuItem>
          <DropdownMenuItem
            onSelect={() => toggleRemoveTeamMember(true)}
            className="rounded-md hover:bg-hover-bg cursor-pointer text-red-500 dark:text-red-400"
          >
            Supprimer le membre
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>

      <Dialog open={updateTeamRole} onOpenChange={toggleUpdateTeamRole}>
        <DialogContent className="sm:max-w-[425px] border-subtle dark:border-white/10 bg-card-bg dark:bg-background-secondary rounded-2xl shadow-custom">
          <DialogHeader>
            <DialogTitle className="text-card-title">
              Mettre à jour le rôle du membre de l'équipe
            </DialogTitle>
            <DialogDescription className="text-foreground/70">
              Mettre à jour le rôle d'un membre de votre équipe
            </DialogDescription>
          </DialogHeader>
          <EditTeamMemberRoleForm
            teamMember={teamMember}
            accountId={accountId}
            isPrimaryOwner={isPrimaryOwner}
          />
        </DialogContent>
      </Dialog>

      <Dialog open={removeTeamMember} onOpenChange={toggleRemoveTeamMember}>
        <DialogContent className="sm:max-w-[425px] border-subtle dark:border-white/10 bg-card-bg dark:bg-background-secondary rounded-2xl shadow-custom">
          <DialogHeader>
            <DialogTitle className="text-card-title">
              Supprimer le membre de l'équipe
            </DialogTitle>
            <DialogDescription className="text-foreground/70">
              Êtes-vous sûr de vouloir supprimer cet utilisateur de l'équipe ?
            </DialogDescription>
          </DialogHeader>
          <DeleteTeamMemberForm teamMember={teamMember} accountId={accountId} />
        </DialogContent>
      </Dialog>
    </>
  );
}
