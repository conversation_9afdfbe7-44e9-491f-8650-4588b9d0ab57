'use client';

import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { Button } from '@/components/ui/button';
import {
  BarChart3,
  Bot,
  Briefcase,
  Settings,
  Sparkles,
  RefreshCw,
  TrendingUp,
  Users,
  Shield,
  Zap,
  Target,
  Brain,
  Globe,
  Heart,
  PenTool,
  Code,
  Camera,
  Calendar,
  DollarSign,
  Rocket,
} from 'lucide-react';

type PromptExample = {
  title: string;
  query: string;
  icon: React.ReactNode;
};

const allPrompts: PromptExample[] = [
  {
    title: 'Tableau de bord d\'étude de marché',
    query: 'Créez un tableau de bord complet d\'étude de marché analysant les tendances de l\'industrie, les segments de clientèle et le paysage concurrentiel. Incluez la visualisation des données et des recommandations exploitables.',
    icon: <BarChart3 className="text-green-700 dark:text-green-400" size={16} />,
  },
  {
    title: 'Moteur de recommandation',
    query: 'Développez un moteur de recommandation pour des suggestions de produits personnalisées. Incluez le filtrage collaboratif, le filtrage basé sur le contenu et des approches hybrides avec des métriques d\'évaluation.',
    icon: <Bot className="text-blue-700 dark:text-blue-400" size={16} />,
  },
  {
    title: 'Stratégie de mise sur le marché',
    query: 'Développez une stratégie complète de mise sur le marché pour un nouveau produit. Incluez la taille du marché, les canaux d\'acquisition de clients, la stratégie de prix et le calendrier de lancement.',
    icon: <Briefcase className="text-rose-700 dark:text-rose-400" size={16} />,
  },
  {
    title: 'Automatisation du pipeline de données',
    query: 'Créez un pipeline de données automatisé pour les processus ETL. Incluez la validation des données, la gestion des erreurs, la surveillance et la conception d\'une architecture évolutive.',
    icon: <Settings className="text-purple-700 dark:text-purple-400" size={16} />,
  },
  {
    title: 'Système de productivité',
    query: 'Concevez un système complet de productivité personnelle comprenant la gestion des tâches, le suivi des objectifs, la formation d\'habitudes et le blocage du temps. Créez des modèles et des flux de travail pour la planification quotidienne, hebdomadaire et mensuelle.',
    icon: <Target className="text-orange-700 dark:text-orange-400" size={16} />,
  },
  {
    title: 'Plan de marketing de contenu',
    query: 'Développez une stratégie de marketing de contenu de 6 mois comprenant des articles de blog, des médias sociaux, des campagnes par e-mail et l\'optimisation SEO. Incluez un calendrier de contenu et des métriques de performance.',
    icon: <PenTool className="text-indigo-700 dark:text-indigo-400" size={16} />,
  },
  {
    title: 'Analyse de portefeuille',
    query: 'Créez un outil d\'analyse de portefeuille d\'investissement personnel avec évaluation des risques, recommandations de diversification et suivi des performances par rapport aux références du marché.',
    icon: <DollarSign className="text-emerald-700 dark:text-emerald-400" size={16} />,
  },
  {
    title: 'Carte du parcours client',
    query: 'Cartographiez le parcours client complet, de la prise de conscience à la promotion. Incluez les points de contact, les points douloureux, les émotions et les opportunités d\'optimisation à chaque étape.',
    icon: <Users className="text-cyan-700 dark:text-cyan-400" size={16} />,
  },
  {
    title: 'Cadre de test A/B',
    query: 'Concevez un cadre complet de test A/B comprenant la formation d\'hypothèses, les calculs de signification statistique et les directives d\'interprétation des résultats.',
    icon: <TrendingUp className="text-teal-700 dark:text-teal-400" size={16} />,
  },
  {
    title: 'Automatisation de la révision de code',
    query: 'Créez un système automatisé de révision de code qui vérifie les vulnérabilités de sécurité, les problèmes de performance et les normes de codage. Incluez l\'intégration avec les pipelines CI/CD.',
    icon: <Code className="text-violet-700 dark:text-violet-400" size={16} />,
  },
  {
    title: 'Matrice d\'évaluation des risques',
    query: 'Développez un cadre complet d\'évaluation des risques pour les opérations commerciales, y compris l\'identification des risques, l\'analyse des probabilités, l\'évaluation de l\'impact et les stratégies d\'atténuation.',
    icon: <Shield className="text-red-700 dark:text-red-400" size={16} />,
  },
  {
    title: 'Générateur de parcours d\'apprentissage',
    query: 'Créez un générateur de parcours d\'apprentissage personnalisé qui s\'adapte aux objectifs individuels, au niveau de compétence actuel et au style d\'apprentissage préféré. Incluez le suivi des progrès et les recommandations de ressources.',
    icon: <Brain className="text-pink-700 dark:text-pink-400" size={16} />,
  },
  {
    title: 'Automatisation des médias sociaux',
    query: 'Concevez un système d\'automatisation des médias sociaux comprenant la planification de contenu, le suivi de l\'engagement, l\'optimisation des hashtags et l\'analyse des performances sur plusieurs plateformes.',
    icon: <Globe className="text-blue-600 dark:text-blue-300" size={16} />,
  },
  {
    title: 'Tableau de bord de suivi de la santé',
    query: 'Construisez un tableau de bord complet de suivi de la santé intégrant les données de fitness, l\'enregistrement de la nutrition, les habitudes de sommeil et les dossiers médicaux avec des informations exploitables et la définition d\'objectifs.',
    icon: <Heart className="text-red-600 dark:text-red-300" size={16} />,
  },
  {
    title: 'Automatisation de projet',
    query: 'Créez un système intelligent de gestion de projet avec attribution automatique des tâches, suivi des délais, allocation des ressources et intégration de la communication d\'équipe.',
    icon: <Calendar className="text-amber-700 dark:text-amber-400" size={16} />,
  },
  {
    title: 'Optimiseur d\'entonnoir de vente',
    query: 'Analysez et optimisez l\'ensemble de l\'entonnoir de vente, de la génération de leads à la conversion. Incluez la notation des leads, les séquences de nurturing et les stratégies d\'optimisation du taux de conversion.',
    icon: <Zap className="text-yellow-600 dark:text-yellow-300" size={16} />,
  },
  {
    title: 'Présentation de startup',
    query: 'Générez un pitch deck de startup convaincant comprenant l\'énoncé du problème, l\'aperçu de la solution, l\'analyse du marché, le modèle commercial, les projections financières et les exigences de financement.',
    icon: <Rocket className="text-orange-600 dark:text-orange-300" size={16} />,
  },
  {
    title: 'Flux de travail photographique',
    query: 'Concevez un flux de travail photographique de bout en bout comprenant la planification de la prise de vue, l\'organisation des fichiers, les préréglages d\'édition, la livraison au client et les systèmes de gestion de portefeuille.',
    icon: <Camera className="text-slate-700 dark:text-slate-400" size={16} />,
  },
  {
    title: 'Analyse de la chaîne d\'approvisionnement',
    query: 'Créez une analyse d\'optimisation de la chaîne d\'approvisionnement comprenant l\'évaluation des fournisseurs, les opportunités de réduction des coûts, l\'atténuation des risques et les stratégies de gestion des stocks.',
    icon: <Briefcase className="text-stone-700 dark:text-stone-400" size={16} />,
  },
  {
    title: 'Cadre de recherche UX',
    query: 'Développez un cadre complet de recherche UX comprenant des entretiens avec les utilisateurs, des tests d\'utilisabilité, le développement de personas et des recommandations de conception basées sur les données.',
    icon: <Sparkles className="text-fuchsia-700 dark:text-fuchsia-400" size={16} />,
  },
];

// Function to get random prompts
const getRandomPrompts = (count: number = 3): PromptExample[] => {
  const shuffled = [...allPrompts].sort(() => 0.5 - Math.random());
  return shuffled.slice(0, count);
};

export const Examples = ({
  onSelectPrompt,
}: {
  onSelectPrompt?: (query: string) => void;
}) => {
  const [displayedPrompts, setDisplayedPrompts] = useState<PromptExample[]>([]);
  const [isRefreshing, setIsRefreshing] = useState(false);

  // Initialize with random prompts on mount
  useEffect(() => {
    setDisplayedPrompts(getRandomPrompts(3));
  }, []);

  const handleRefresh = () => {
    setIsRefreshing(true);
    setDisplayedPrompts(getRandomPrompts(3));
    setTimeout(() => setIsRefreshing(false), 300);
  };

  return (
    <div className="w-full max-w-4xl mx-auto px-4">
      <div className="group relative">
        <div className="flex gap-2 justify-center py-2">
          {displayedPrompts.map((prompt, index) => (
            <motion.div
              key={`${prompt.title}-${index}`}
              initial={{ opacity: 0, scale: 0.8 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{
                duration: 0.3,
                delay: index * 0.03,
                ease: "easeOut"
              }}
            >
              <Button
                variant="outline"
                className="w-fit h-fit px-3 py-2 rounded-full border-neutral-200 dark:border-neutral-800 bg-neutral-50 hover:bg-neutral-100 dark:bg-neutral-900 dark:hover:bg-neutral-800 text-sm font-normal text-muted-foreground hover:text-foreground transition-colors"
                onClick={() => onSelectPrompt && onSelectPrompt(prompt.query)}
              >
                <div className="flex items-center gap-2">
                  <div className="flex-shrink-0">
                    {React.cloneElement(prompt.icon as React.ReactElement, { size: 14 })}
                  </div>
                  <span className="whitespace-nowrap">{prompt.title}</span>
                </div>
              </Button>
            </motion.div>
          ))}
        </div>

        {/* Refresh button that appears on hover */}
        <Button
          variant="ghost"
          size="sm"
          onClick={handleRefresh}
          className="absolute -top-4 right-1 h-5 w-5 p-0 rounded-full opacity-0 group-hover:opacity-100 transition-opacity duration-200 hover:bg-neutral-100 dark:hover:bg-neutral-800"
        >
          <motion.div
            animate={{ rotate: isRefreshing ? 360 : 0 }}
            transition={{ duration: 0.3, ease: "easeInOut" }}
          >
            <RefreshCw size={10} className="text-muted-foreground" />
          </motion.div>
        </Button>
      </div>
    </div>
  );
};