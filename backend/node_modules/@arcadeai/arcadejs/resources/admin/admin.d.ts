import { APIResource } from "../../resource.js";
import * as AuthProvidersAPI from "./auth-providers.js";
import { AuthProviderCreateParams, AuthProviderCreateRequest, AuthProviderListResponse, AuthProviderPatchParams, AuthProviderResponse, AuthProviderUpdateRequest, AuthProviders } from "./auth-providers.js";
import * as SecretsAPI from "./secrets.js";
import { SecretListResponse, SecretResponse, Secrets } from "./secrets.js";
import * as UserConnectionsAPI from "./user-connections.js";
import { UserConnectionListParams, UserConnectionResponse, UserConnectionResponsesOffsetPage, UserConnections } from "./user-connections.js";
export declare class Admin extends APIResource {
    userConnections: UserConnectionsAPI.UserConnections;
    authProviders: AuthProvidersAPI.AuthProviders;
    secrets: SecretsAPI.Secrets;
}
export declare namespace Admin {
    export { UserConnections as UserConnections, type UserConnectionResponse as UserConnectionResponse, UserConnectionResponsesOffsetPage as UserConnectionResponsesOffsetPage, type UserConnectionListParams as UserConnectionListParams, };
    export { AuthProviders as AuthProviders, type AuthProviderCreateRequest as AuthProviderCreateRequest, type AuthProviderResponse as AuthProviderResponse, type AuthProviderUpdateRequest as AuthProviderUpdateRequest, type AuthProviderListResponse as AuthProviderListResponse, type AuthProviderCreateParams as AuthProviderCreateParams, type AuthProviderPatchParams as AuthProviderPatchParams, };
    export { Secrets as Secrets, type SecretResponse as SecretResponse, type SecretListResponse as SecretListResponse, };
}
//# sourceMappingURL=admin.d.ts.map