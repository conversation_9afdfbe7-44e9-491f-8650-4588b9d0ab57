import { ThemeProvider } from '@/components/home/<USER>';
import { siteConfig } from '@/lib/site';
import type { Metadata, Viewport } from 'next';
import { <PERSON><PERSON><PERSON>, <PERSON>eist_Mono } from 'next/font/google';
import './globals.css';
import { Providers } from './providers';
import { Toaster } from '@/components/ui/sonner';
import { Analytics } from '@vercel/analytics/react';
import { GoogleAnalytics } from '@next/third-parties/google';
import { SpeedInsights } from '@vercel/speed-insights/next';
import Script from 'next/script';

const geistSans = Geist({
  variable: '--font-geist-sans',
  subsets: ['latin'],
});

const geistMono = Geist_Mono({
  variable: '--font-geist-mono',
  subsets: ['latin'],
});

export const viewport: Viewport = {
  themeColor: 'black',
};

export const metadata: Metadata = {
  metadataBase: new URL(siteConfig.url),
  title: {
    default: siteConfig.name,
    template: `%s - ${siteConfig.name}`,
  },
  description:
    "Alex d'Orchestra Connect est votre conciergerie d'affaires augmentée. Cet assistant IA intelligent optimise le quotidien de votre PME française avec des solutions personnalisées et une interface entièrement en français.",
  keywords: [
    'Orchestra Connect',
    'IA pour PME',
    'Agent IA',
    'Intelligence Artificielle',
    'Conciergerie IA',
    'Productivité PME',
    'Assistant IA français',
    'Alex IA',
    'Automatisation PME',
  ],

  authors: [{ name: 'Orchestra Connect', url: 'https://orchestraconnect.fr' }],
  creator: 'Orchestra Connect - Agent IA général Français',
  publisher: 'Orchestra Connect - Agent IA général Français',
  category: 'Technology',
  applicationName: 'Orchestra Connect',
  formatDetection: {
    telephone: false,
    email: false,
    address: false,
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
    },
  },
  openGraph: {
    title: 'Orchestra Connect - Agent IA général Français',
    description:
      "Alex d'Orchestra Connect est votre conciergerie d'affaires augmentée. Cet assistant IA intelligent optimise le quotidien de votre PME française avec des solutions personnalisées et une interface entièrement en français.",
    url: siteConfig.url,
    siteName: 'Orchestra Connect',
    images: [
      {
        url: '/banner.png',
        width: 1200,
        height: 630,
        alt: 'Orchestra Connect - Agent IA général Français',
        type: 'image/png',
      },
    ],
    locale: 'fr_FR',
    type: 'website',
  },
  twitter: {
    card: 'summary_large_image',
    title: 'Orchestra Connect - Agent IA général Français',
    description:
      'Alex d\'Orchestra Connect optimise le quotidien de votre PME avec des solutions IA personnalisées et une interface entièrement en français.',
    creator: '@orchestraconnect',
    site: '@orchestraconnect',
    images: [
      {
        url: '/banner.png',
        width: 1200,
        height: 630,
        alt: 'Orchestra Connect - Agent IA général Français',
      },
    ],
  },
  icons: {
    icon: [{ url: '/favicon.png', sizes: 'any' }],
    shortcut: '/favicon.png',
  },
  // manifest: "/manifest.json",
  alternates: {
    canonical: siteConfig.url,
  },
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" suppressHydrationWarning>
      <head>
        {/* Google Tag Manager */}
        <Script id="google-tag-manager" strategy="afterInteractive">
          {`(function(w,d,s,l,i){w[l]=w[l]||[];w[l].push({'gtm.start':
          new Date().getTime(),event:'gtm.js'});var f=d.getElementsByTagName(s)[0],
          j=d.createElement(s),dl=l!='dataLayer'?'&l='+l:'';j.async=true;j.src=
          'https://www.googletagmanager.com/gtm.js?id='+i+dl;f.parentNode.insertBefore(j,f);
          })(window,document,'script','dataLayer','GTM-NP9Q5QZH');`}
        </Script>
        <Script async src="https://cdn.tolt.io/tolt.js" data-tolt={process.env.NEXT_PUBLIC_TOLT_REFERRAL_ID}></Script>
      </head>

      <body
        className={`${geistSans.variable} ${geistMono.variable} antialiased font-sans bg-background`}
      >
        <noscript>
          <iframe
            src="https://www.googletagmanager.com/ns.html?id=GTM-NP9Q5QZH"
            height="0"
            width="0"
            style={{ display: 'none', visibility: 'hidden' }}
          />
        </noscript>
        {/* End Google Tag Manager (noscript) */}

        <ThemeProvider
          attribute="class"
          defaultTheme="system"
          enableSystem
          disableTransitionOnChange
        >
          <Providers>
            {children}
            <Toaster />
          </Providers>
          <Analytics />
          <GoogleAnalytics gaId="G-YJMXVLC1BN" />
          <SpeedInsights />
        </ThemeProvider>
      </body>
    </html>
  );
}
