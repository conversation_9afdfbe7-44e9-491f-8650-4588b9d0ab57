-- Migration pour créer la table arcade_profiles
-- Compatible avec la structure Supabase existante

CREATE TABLE IF NOT EXISTS arcade_profiles (
    profile_id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    account_id UUID NOT NULL,
    profile_name <PERSON><PERSON><PERSON><PERSON>(255) NOT NULL,
    toolkit_name VA<PERSON>HAR(100) NOT NULL,
    app_name VA<PERSON><PERSON><PERSON>(255) NOT NULL,
    description TEXT,
    is_active BOOLEAN DEFAULT true,
    is_default BOOLEAN DEFAULT false,
    enabled_tools JSONB DEFAULT '[]'::jsonb,
    external_user_id VARCHAR(255),
    oauth_app_id VARCHAR(255),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Contraintes
    CONSTRAINT arcade_profiles_account_profile_toolkit_unique 
        UNIQUE (account_id, profile_name, toolkit_name),
    
    -- Index pour les requêtes fréquentes
    INDEX idx_arcade_profiles_account_id ON arcade_profiles(account_id),
    INDEX idx_arcade_profiles_toolkit_name ON arcade_profiles(toolkit_name),
    INDEX idx_arcade_profiles_is_active ON arcade_profiles(is_active),
    INDEX idx_arcade_profiles_is_default ON arcade_profiles(is_default),
    INDEX idx_arcade_profiles_external_user_id ON arcade_profiles(external_user_id)
);

-- Trigger pour mettre à jour updated_at automatiquement
CREATE OR REPLACE FUNCTION update_arcade_profiles_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER arcade_profiles_updated_at_trigger
    BEFORE UPDATE ON arcade_profiles
    FOR EACH ROW
    EXECUTE FUNCTION update_arcade_profiles_updated_at();

-- Commentaires pour la documentation
COMMENT ON TABLE arcade_profiles IS 'Profils utilisateur pour l''intégration Arcade.dev';
COMMENT ON COLUMN arcade_profiles.profile_id IS 'Identifiant unique du profil';
COMMENT ON COLUMN arcade_profiles.account_id IS 'Identifiant du compte utilisateur';
COMMENT ON COLUMN arcade_profiles.profile_name IS 'Nom du profil';
COMMENT ON COLUMN arcade_profiles.toolkit_name IS 'Nom du toolkit Arcade (ex: gmail, slack)';
COMMENT ON COLUMN arcade_profiles.app_name IS 'Nom d''affichage de l''application';
COMMENT ON COLUMN arcade_profiles.description IS 'Description optionnelle du profil';
COMMENT ON COLUMN arcade_profiles.is_active IS 'Indique si le profil est actif';
COMMENT ON COLUMN arcade_profiles.is_default IS 'Indique si c''est le profil par défaut pour ce toolkit';
COMMENT ON COLUMN arcade_profiles.enabled_tools IS 'Liste des outils activés (JSON)';
COMMENT ON COLUMN arcade_profiles.external_user_id IS 'ID utilisateur externe pour Arcade';
COMMENT ON COLUMN arcade_profiles.oauth_app_id IS 'ID de l''application OAuth si applicable';
