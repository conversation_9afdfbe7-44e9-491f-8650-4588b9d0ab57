import { Button } from '@/components/ui/button';
import Link from 'next/link';
import { Users, ExternalLink } from 'lucide-react';
import { HelpModal } from './help-modal';

export function CTACard() {
  return (
    <div className="space-y-3">
      {/* Card Entreprise réduite */}
      <div className="rounded-xl bg-gradient-to-br from-green-50 to-green-200 dark:from-green-950/40 dark:to-green-900/40 shadow-sm border border-green-200/50 dark:border-green-800/50 p-3 transition-all">
        <div className="flex flex-col space-y-2">
          <div className="flex flex-col">
            <span className="text-sm font-medium text-foreground">
              🏢 Entreprise
            </span>
            <span className="text-xs text-muted-foreground">
              Pilotage d'employés IA sur mesure
            </span>
          </div>

          <div className="flex items-center">
            <Link
              href="https://orchestraconciergerie.fr"
              target="_blank"
              rel="noopener noreferrer"
              className="flex items-center text-xs text-green-700 hover:text-green-800 dark:text-green-400 dark:hover:text-green-300 transition-colors font-medium"
            >
              <Users className="mr-1.5 h-3.5 w-3.5" />
              orchestraconciergerie.fr
              <ExternalLink className="ml-1 h-3 w-3" />
            </Link>
          </div>
        </div>
      </div>

      {/* Bouton d'aide avec style glassmorphisme bleu */}
      <HelpModal />
    </div>
  );
}
