// File generated from our OpenAPI spec by Stainless. See CONTRIBUTING.md for details.
import { APIResource } from "../../resource.mjs";
import { isRequestOptions } from "../../core.mjs";
import * as FormattedAPI from "./formatted.mjs";
import { Formatted, FormattedListResponsesOffsetPage, } from "./formatted.mjs";
import * as ScheduledAPI from "./scheduled.mjs";
import { Scheduled } from "./scheduled.mjs";
import { OffsetPage } from "../../pagination.mjs";
export class Tools extends APIResource {
    constructor() {
        super(...arguments);
        this.scheduled = new ScheduledAPI.Scheduled(this._client);
        this.formatted = new FormattedAPI.Formatted(this._client);
    }
    list(query = {}, options) {
        if (isRequestOptions(query)) {
            return this.list({}, query);
        }
        return this._client.getAPIList('/v1/tools', ToolDefinitionsOffsetPage, { query, ...options });
    }
    /**
     * Authorizes a user for a specific tool by name
     */
    authorize(body, options) {
        return this._client.post('/v1/tools/authorize', { body, ...options });
    }
    /**
     * Executes a tool by name and arguments
     */
    execute(body, options) {
        return this._client.post('/v1/tools/execute', { body, ...options });
    }
    get(name, query = {}, options) {
        if (isRequestOptions(query)) {
            return this.get(name, {}, query);
        }
        return this._client.get(`/v1/tools/${name}`, { query, ...options });
    }
}
export class ToolDefinitionsOffsetPage extends OffsetPage {
}
export class ToolExecutionsOffsetPage extends OffsetPage {
}
Tools.ToolDefinitionsOffsetPage = ToolDefinitionsOffsetPage;
Tools.Scheduled = Scheduled;
Tools.Formatted = Formatted;
Tools.FormattedListResponsesOffsetPage = FormattedListResponsesOffsetPage;
//# sourceMappingURL=tools.mjs.map