'use client';

import React, { useState, useMemo } from 'react';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Search, Filter, Loader2, Grid, List } from 'lucide-react';
import { ArcadeConnector } from './arcade-connector';
import { useArcadeToolkits, useArcadePopularToolkits } from '@/hooks/react-query/arcade/use-arcade';
import type { ArcadeToolkit } from '@/hooks/react-query/arcade/use-arcade';

interface ArcadeRegistryProps {
  selectedAgentId?: string;
  onToolkitConnected?: (toolkit: ArcadeToolkit) => void;
  className?: string;
}

export function ArcadeRegistry({
  selectedAgentId,
  onToolkitConnected,
  className
}: ArcadeRegistryProps) {
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedCategory, setSelectedCategory] = useState<string>('all');
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');

  // Récupérer les toolkits populaires
  const { 
    data: popularToolkits, 
    isLoading: isLoadingPopular,
    error: popularError 
  } = useArcadePopularToolkits();

  // Rechercher dans tous les toolkits
  const { 
    data: searchResults, 
    isLoading: isLoadingSearch,
    error: searchError 
  } = useArcadeToolkits(searchQuery || undefined, selectedCategory !== 'all' ? selectedCategory : undefined);

  // Catégories disponibles
  const categories = useMemo(() => {
    const allToolkits = [
      ...(popularToolkits?.toolkits || []),
      ...(searchResults?.toolkits || [])
    ];
    
    const categorySet = new Set(allToolkits.map(t => t.category));
    return ['all', ...Array.from(categorySet).sort()];
  }, [popularToolkits, searchResults]);

  // Toolkits filtrés
  const filteredToolkits = useMemo(() => {
    if (searchQuery) {
      return searchResults?.toolkits || [];
    }
    
    let toolkits = popularToolkits?.toolkits || [];
    
    if (selectedCategory !== 'all') {
      toolkits = toolkits.filter(t => t.category === selectedCategory);
    }
    
    return toolkits;
  }, [searchQuery, selectedCategory, popularToolkits, searchResults]);

  const handleToolkitConnected = (toolkit: ArcadeToolkit) => {
    onToolkitConnected?.(toolkit);
  };

  const isLoading = isLoadingPopular || isLoadingSearch;
  const hasError = popularError || searchError;

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Header */}
      <div className="space-y-4">
        <div>
          <h2 className="text-2xl font-bold">Arcade Toolkits</h2>
          <p className="text-muted-foreground">
            Connect your agents to powerful tools and services via Arcade.dev
          </p>
        </div>

        {/* Recherche et filtres */}
        <div className="flex flex-col sm:flex-row gap-4">
          <div className="relative flex-1">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="Search toolkits..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-10"
            />
          </div>
          
          <div className="flex gap-2">
            <select
              value={selectedCategory}
              onChange={(e) => setSelectedCategory(e.target.value)}
              className="px-3 py-2 border border-input bg-background rounded-md text-sm"
            >
              {categories.map((category) => (
                <option key={category} value={category}>
                  {category === 'all' ? 'All Categories' : category.charAt(0).toUpperCase() + category.slice(1)}
                </option>
              ))}
            </select>

            <Button
              variant="outline"
              size="sm"
              onClick={() => setViewMode(viewMode === 'grid' ? 'list' : 'grid')}
            >
              {viewMode === 'grid' ? <List className="h-4 w-4" /> : <Grid className="h-4 w-4" />}
            </Button>
          </div>
        </div>
      </div>

      {/* Contenu */}
      <Tabs defaultValue="browse" className="w-full">
        <TabsList className="grid w-full grid-cols-2">
          <TabsTrigger value="browse">Browse Toolkits</TabsTrigger>
          <TabsTrigger value="popular">Popular</TabsTrigger>
        </TabsList>

        <TabsContent value="browse" className="space-y-4">
          {isLoading ? (
            <div className="flex items-center justify-center py-12">
              <Loader2 className="h-8 w-8 animate-spin" />
              <span className="ml-2">Loading toolkits...</span>
            </div>
          ) : hasError ? (
            <div className="text-center py-12">
              <p className="text-red-500">Error loading toolkits</p>
              <Button variant="outline" onClick={() => window.location.reload()}>
                Retry
              </Button>
            </div>
          ) : filteredToolkits.length === 0 ? (
            <div className="text-center py-12">
              <p className="text-muted-foreground">
                {searchQuery ? 'No toolkits found matching your search' : 'No toolkits available'}
              </p>
            </div>
          ) : (
            <div className={
              viewMode === 'grid' 
                ? "grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4"
                : "space-y-4"
            }>
              {filteredToolkits.map((toolkit) => (
                <ArcadeConnector
                  key={toolkit.name}
                  toolkit={toolkit}
                  selectedAgentId={selectedAgentId}
                  onConnectionSuccess={handleToolkitConnected}
                />
              ))}
            </div>
          )}
        </TabsContent>

        <TabsContent value="popular" className="space-y-4">
          {isLoadingPopular ? (
            <div className="flex items-center justify-center py-12">
              <Loader2 className="h-8 w-8 animate-spin" />
              <span className="ml-2">Loading popular toolkits...</span>
            </div>
          ) : popularError ? (
            <div className="text-center py-12">
              <p className="text-red-500">Error loading popular toolkits</p>
            </div>
          ) : (
            <div className="space-y-6">
              {/* Statistiques */}
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <Card>
                  <CardHeader className="pb-2">
                    <CardTitle className="text-sm font-medium">Total Toolkits</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold">{popularToolkits?.count || 0}</div>
                  </CardContent>
                </Card>
                
                <Card>
                  <CardHeader className="pb-2">
                    <CardTitle className="text-sm font-medium">Categories</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold">{categories.length - 1}</div>
                  </CardContent>
                </Card>
                
                <Card>
                  <CardHeader className="pb-2">
                    <CardTitle className="text-sm font-medium">Most Popular</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="text-sm font-medium">
                      {popularToolkits?.toolkits?.[0]?.display_name || 'N/A'}
                    </div>
                  </CardContent>
                </Card>
              </div>

              {/* Toolkits populaires */}
              <div className={
                viewMode === 'grid' 
                  ? "grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4"
                  : "space-y-4"
              }>
                {popularToolkits?.toolkits?.map((toolkit) => (
                  <ArcadeConnector
                    key={toolkit.name}
                    toolkit={toolkit}
                    selectedAgentId={selectedAgentId}
                    onConnectionSuccess={handleToolkitConnected}
                  />
                ))}
              </div>
            </div>
          )}
        </TabsContent>
      </Tabs>
    </div>
  );
}
