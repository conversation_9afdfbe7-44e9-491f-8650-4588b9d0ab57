"use strict";
// File generated from our OpenAPI spec by Stainless. See CONTRIBUTING.md for details.
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ToolExecutionsOffsetPage = exports.ToolDefinitionsOffsetPage = exports.Tools = void 0;
const resource_1 = require("../../resource.js");
const core_1 = require("../../core.js");
const FormattedAPI = __importStar(require("./formatted.js"));
const formatted_1 = require("./formatted.js");
const ScheduledAPI = __importStar(require("./scheduled.js"));
const scheduled_1 = require("./scheduled.js");
const pagination_1 = require("../../pagination.js");
class Tools extends resource_1.APIResource {
    constructor() {
        super(...arguments);
        this.scheduled = new ScheduledAPI.Scheduled(this._client);
        this.formatted = new FormattedAPI.Formatted(this._client);
    }
    list(query = {}, options) {
        if ((0, core_1.isRequestOptions)(query)) {
            return this.list({}, query);
        }
        return this._client.getAPIList('/v1/tools', ToolDefinitionsOffsetPage, { query, ...options });
    }
    /**
     * Authorizes a user for a specific tool by name
     */
    authorize(body, options) {
        return this._client.post('/v1/tools/authorize', { body, ...options });
    }
    /**
     * Executes a tool by name and arguments
     */
    execute(body, options) {
        return this._client.post('/v1/tools/execute', { body, ...options });
    }
    get(name, query = {}, options) {
        if ((0, core_1.isRequestOptions)(query)) {
            return this.get(name, {}, query);
        }
        return this._client.get(`/v1/tools/${name}`, { query, ...options });
    }
}
exports.Tools = Tools;
class ToolDefinitionsOffsetPage extends pagination_1.OffsetPage {
}
exports.ToolDefinitionsOffsetPage = ToolDefinitionsOffsetPage;
class ToolExecutionsOffsetPage extends pagination_1.OffsetPage {
}
exports.ToolExecutionsOffsetPage = ToolExecutionsOffsetPage;
Tools.ToolDefinitionsOffsetPage = ToolDefinitionsOffsetPage;
Tools.Scheduled = scheduled_1.Scheduled;
Tools.Formatted = formatted_1.Formatted;
Tools.FormattedListResponsesOffsetPage = formatted_1.FormattedListResponsesOffsetPage;
//# sourceMappingURL=tools.js.map