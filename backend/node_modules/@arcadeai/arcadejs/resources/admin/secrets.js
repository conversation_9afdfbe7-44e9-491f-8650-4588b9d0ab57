"use strict";
// File generated from our OpenAPI spec by Stainless. See CONTRIBUTING.md for details.
Object.defineProperty(exports, "__esModule", { value: true });
exports.Secrets = void 0;
const resource_1 = require("../../resource.js");
class Secrets extends resource_1.APIResource {
    /**
     * List all secrets that are visible to the caller
     */
    list(options) {
        return this._client.get('/v1/admin/secrets', options);
    }
    /**
     * Delete a secret by its ID
     */
    delete(secretId, options) {
        return this._client.delete(`/v1/admin/secrets/${secretId}`, {
            ...options,
            headers: { Accept: '*/*', ...options?.headers },
        });
    }
}
exports.Secrets = Secrets;
//# sourceMappingURL=secrets.js.map