// File generated from our OpenAPI spec by Stainless. See CONTRIBUTING.md for details.
import { APIResource } from "../../resource.mjs";
import * as AuthProvidersAPI from "./auth-providers.mjs";
import { AuthProviders, } from "./auth-providers.mjs";
import * as SecretsAPI from "./secrets.mjs";
import { Secrets } from "./secrets.mjs";
import * as UserConnectionsAPI from "./user-connections.mjs";
import { UserConnectionResponsesOffsetPage, UserConnections, } from "./user-connections.mjs";
export class Admin extends APIResource {
    constructor() {
        super(...arguments);
        this.userConnections = new UserConnectionsAPI.UserConnections(this._client);
        this.authProviders = new AuthProvidersAPI.AuthProviders(this._client);
        this.secrets = new SecretsAPI.Secrets(this._client);
    }
}
Admin.UserConnections = UserConnections;
Admin.UserConnectionResponsesOffsetPage = UserConnectionResponsesOffsetPage;
Admin.AuthProviders = AuthProviders;
Admin.Secrets = Secrets;
//# sourceMappingURL=admin.mjs.map