import { acceptInvitation } from '@/lib/actions/invitations';
import { createClient } from '@/lib/supabase/server';
import { Alert } from '../ui/alert';
import { Card, CardContent } from '../ui/card';
import { SubmitButton } from '../ui/submit-button';

type Props = {
  token: string;
};
export default async function AcceptTeamInvitation({ token }: Props) {
  const supabaseClient = await createClient();
  const { data: invitation } = await supabaseClient.rpc('lookup_invitation', {
    lookup_invitation_token: token,
  });

  return (
    <Card>
      <CardContent className="p-8 text-center flex flex-col gap-y-8">
        <div>
          <p>Vous avez été invité à rejoindre</p>
          <h1 className="text-xl font-bold">{invitation.account_name}</h1>
        </div>
        {Boolean(invitation.active) ? (
          <form>
            <input type="hidden" name="token" value={token} />
            <SubmitButton
              formAction={acceptInvitation}
              pendingText="Acceptation de l'invitation..."
            >
              Accepter l'invitation
            </SubmitButton>
          </form>
        ) : (
          <Alert variant="destructive">
            Cette invitation a été désactivée. Veuillez contacter le propriétaire du compte pour une nouvelle invitation.
          </Alert>
        )}
      </CardContent>
    </Card>
  );
}
