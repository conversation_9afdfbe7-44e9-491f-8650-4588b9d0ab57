"use strict";
// File generated from our OpenAPI spec by Stainless. See CONTRIBUTING.md for details.
Object.defineProperty(exports, "__esModule", { value: true });
exports.FormattedListResponsesOffsetPage = exports.Formatted = void 0;
const resource_1 = require("../../resource.js");
const core_1 = require("../../core.js");
const pagination_1 = require("../../pagination.js");
class Formatted extends resource_1.APIResource {
    list(query = {}, options) {
        if ((0, core_1.isRequestOptions)(query)) {
            return this.list({}, query);
        }
        return this._client.getAPIList('/v1/formatted_tools', FormattedListResponsesOffsetPage, {
            query,
            ...options,
        });
    }
    get(name, query = {}, options) {
        if ((0, core_1.isRequestOptions)(query)) {
            return this.get(name, {}, query);
        }
        return this._client.get(`/v1/formatted_tools/${name}`, { query, ...options });
    }
}
exports.Formatted = Formatted;
class FormattedListResponsesOffsetPage extends pagination_1.OffsetPage {
}
exports.FormattedListResponsesOffsetPage = FormattedListResponsesOffsetPage;
Formatted.FormattedListResponsesOffsetPage = FormattedListResponsesOffsetPage;
//# sourceMappingURL=formatted.js.map