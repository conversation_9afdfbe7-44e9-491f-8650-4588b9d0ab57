// File generated from our OpenAPI spec by Stainless. See CONTRIBUTING.md for details.
import { APIResource } from "../../resource.mjs";
import { isRequestOptions } from "../../core.mjs";
import { ToolExecutionsOffsetPage } from "./tools.mjs";
export class Scheduled extends APIResource {
    list(query = {}, options) {
        if (isRequestOptions(query)) {
            return this.list({}, query);
        }
        return this._client.getAPIList('/v1/scheduled_tools', ToolExecutionsOffsetPage, { query, ...options });
    }
    /**
     * Returns the details for a specific scheduled tool execution
     */
    get(id, options) {
        return this._client.get(`/v1/scheduled_tools/${id}`, options);
    }
}
export { ToolExecutionsOffsetPage };
//# sourceMappingURL=scheduled.mjs.map