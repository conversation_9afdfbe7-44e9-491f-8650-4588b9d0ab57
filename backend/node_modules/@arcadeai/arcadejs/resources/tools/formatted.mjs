// File generated from our OpenAPI spec by Stainless. See CONTRIBUTING.md for details.
import { APIResource } from "../../resource.mjs";
import { isRequestOptions } from "../../core.mjs";
import { OffsetPage } from "../../pagination.mjs";
export class Formatted extends APIResource {
    list(query = {}, options) {
        if (isRequestOptions(query)) {
            return this.list({}, query);
        }
        return this._client.getAPIList('/v1/formatted_tools', FormattedListResponsesOffsetPage, {
            query,
            ...options,
        });
    }
    get(name, query = {}, options) {
        if (isRequestOptions(query)) {
            return this.get(name, {}, query);
        }
        return this._client.get(`/v1/formatted_tools/${name}`, { query, ...options });
    }
}
export class FormattedListResponsesOffsetPage extends OffsetPage {
}
Formatted.FormattedListResponsesOffsetPage = FormattedListResponsesOffsetPage;
//# sourceMappingURL=formatted.mjs.map