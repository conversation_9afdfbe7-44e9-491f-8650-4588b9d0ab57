from typing import List, Dict, Any, Optional
from ..domain.entities import Tool, ToolkitInfo
from ..domain.value_objects import UserId, ToolkitName, SearchQuery, Category
from ..domain.exceptions import ToolkitNotFoundError, ToolNotFoundError
from ..support.arcade_client import ArcadeClient
from ..protocols import Logger


class ToolService:
    """
    Service de gestion des outils Arcade.dev
    """
    
    def __init__(self, tool_repository, arcade_client: ArcadeClient, logger: Logger):
        self._tool_repo = tool_repository
        self._arcade_client = arcade_client
        self._logger = logger
        self._toolkit_cache = {}  # Cache simple pour les toolkits
    
    async def discover_tools(
        self, 
        user_id: UserId, 
        toolkit_name: Optional[ToolkitName] = None
    ) -> List[Tool]:
        """
        Découvre les outils disponibles pour un utilisateur
        """
        try:
            self._logger.info(f"Discovering tools for user {user_id.value}")
            
            if toolkit_name:
                # Découvrir les outils d'un toolkit spécifique
                tools = await self._arcade_client.get_toolkit_tools(
                    toolkit_name.value, 
                    user_id.value
                )
                
                tool_objects = []
                for tool_data in tools:
                    tool = Tool(
                        name=tool_data["name"],
                        description=tool_data["description"],
                        toolkit_name=toolkit_name.value,
                        input_schema=tool_data.get("input_schema", {}),
                        requires_auth=tool_data.get("requires_auth", False)
                    )
                    tool_objects.append(tool)
                
                self._logger.info(f"Discovered {len(tool_objects)} tools for toolkit {toolkit_name.value}")
                return tool_objects
            else:
                # Découvrir tous les outils disponibles
                all_tools = []
                toolkits_response = await self._arcade_client.list_toolkits()
                
                for toolkit_data in toolkits_response.get("toolkits", []):
                    toolkit_tools = await self._arcade_client.get_toolkit_tools(
                        toolkit_data["name"], 
                        user_id.value
                    )
                    
                    for tool_data in toolkit_tools:
                        tool = Tool(
                            name=tool_data["name"],
                            description=tool_data["description"],
                            toolkit_name=toolkit_data["name"],
                            input_schema=tool_data.get("input_schema", {}),
                            requires_auth=tool_data.get("requires_auth", False)
                        )
                        all_tools.append(tool)
                
                self._logger.info(f"Discovered {len(all_tools)} total tools")
                return all_tools
                
        except Exception as e:
            self._logger.error(f"Error discovering tools: {str(e)}")
            return []
    
    async def create_toolkit_connection(
        self, 
        user_id: UserId, 
        toolkit_name: ToolkitName, 
        oauth_app_id: Optional[str] = None
    ) -> ToolkitInfo:
        """
        Crée une connexion à un toolkit
        """
        try:
            self._logger.info(f"Creating toolkit connection for {toolkit_name.value}")
            
            # Récupérer les informations du toolkit
            toolkit_info = await self.get_toolkit_by_name(toolkit_name)
            if not toolkit_info:
                raise ToolkitNotFoundError(f"Toolkit {toolkit_name.value} not found")
            
            # Récupérer les outils du toolkit
            tools = await self._arcade_client.get_toolkit_tools(
                toolkit_name.value, 
                user_id.value
            )
            
            # Créer les objets Tool
            tool_objects = []
            for tool_data in tools:
                tool = Tool(
                    name=tool_data["name"],
                    description=tool_data["description"],
                    toolkit_name=toolkit_name.value,
                    input_schema=tool_data.get("input_schema", {}),
                    requires_auth=tool_data.get("requires_auth", False)
                )
                tool_objects.append(tool)
            
            # Mettre à jour le ToolkitInfo avec les outils
            toolkit_info.available_tools = tool_objects
            
            self._logger.info(f"Created connection to {toolkit_name.value} with {len(tool_objects)} tools")
            return toolkit_info
            
        except ToolkitNotFoundError:
            raise
        except Exception as e:
            self._logger.error(f"Error creating toolkit connection: {str(e)}")
            raise
    
    async def search_toolkits(
        self,
        query: Optional[str] = None,
        category: Optional[str] = None,
        page: int = 1,
        limit: int = 20
    ) -> Dict[str, Any]:
        """
        Recherche dans les toolkits disponibles
        """
        try:
            self._logger.info(f"Searching toolkits with query: {query}, category: {category}")
            
            # Utiliser le client Arcade pour lister les toolkits
            toolkits_response = await self._arcade_client.list_toolkits(
                category=category,
                search=query,
                limit=limit
            )
            
            # Calculer la pagination
            start_index = (page - 1) * limit
            end_index = start_index + limit
            
            toolkits = toolkits_response.get("toolkits", [])
            paginated_toolkits = toolkits[start_index:end_index]
            
            return {
                "toolkits": paginated_toolkits,
                "total": len(toolkits),
                "page": page,
                "limit": limit,
                "has_more": end_index < len(toolkits)
            }
            
        except Exception as e:
            self._logger.error(f"Error searching toolkits: {str(e)}")
            return {
                "toolkits": [],
                "total": 0,
                "page": page,
                "limit": limit,
                "has_more": False
            }
    
    async def get_toolkit_by_name(self, toolkit_name: ToolkitName) -> Optional[ToolkitInfo]:
        """
        Récupère un toolkit par nom
        """
        try:
            # Vérifier le cache d'abord
            if toolkit_name.value in self._toolkit_cache:
                return self._toolkit_cache[toolkit_name.value]
            
            # Lister tous les toolkits et trouver celui qui correspond
            toolkits_response = await self._arcade_client.list_toolkits()
            
            for toolkit_data in toolkits_response.get("toolkits", []):
                if toolkit_data["name"] == toolkit_name.value:
                    toolkit_info = ToolkitInfo(
                        name=toolkit_data["name"],
                        display_name=toolkit_data["display_name"],
                        description=toolkit_data["description"],
                        category=toolkit_data["category"]
                    )
                    
                    # Mettre en cache
                    self._toolkit_cache[toolkit_name.value] = toolkit_info
                    return toolkit_info
            
            return None
            
        except Exception as e:
            self._logger.error(f"Error getting toolkit by name: {str(e)}")
            return None
    
    async def get_popular_toolkits(
        self, 
        category: Optional[str] = None, 
        limit: int = 100
    ) -> List[ToolkitInfo]:
        """
        Récupère les toolkits populaires
        """
        try:
            self._logger.info(f"Getting popular toolkits, category: {category}")
            
            toolkits_response = await self._arcade_client.list_toolkits(
                category=category,
                limit=limit
            )
            
            toolkit_infos = []
            for toolkit_data in toolkits_response.get("toolkits", []):
                toolkit_info = ToolkitInfo(
                    name=toolkit_data["name"],
                    display_name=toolkit_data["display_name"],
                    description=toolkit_data["description"],
                    category=toolkit_data["category"]
                )
                toolkit_infos.append(toolkit_info)
            
            return toolkit_infos
            
        except Exception as e:
            self._logger.error(f"Error getting popular toolkits: {str(e)}")
            return []
    
    async def execute_tool(
        self, 
        user_id: UserId, 
        tool_name: str, 
        input_data: Dict[str, Any]
    ) -> Dict[str, Any]:
        """
        Exécute un outil Arcade
        """
        try:
            self._logger.info(f"Executing tool {tool_name} for user {user_id.value}")
            
            result = await self._arcade_client.execute_tool(
                tool_name, 
                user_id.value, 
                input_data
            )
            
            if result.get("success"):
                self._logger.info(f"Tool {tool_name} executed successfully")
            else:
                self._logger.error(f"Tool {tool_name} execution failed: {result.get('error')}")
            
            return result
            
        except Exception as e:
            self._logger.error(f"Error executing tool {tool_name}: {str(e)}")
            return {
                "success": False,
                "error": str(e),
                "tool_name": tool_name
            }
    
    def clear_cache(self):
        """Vide le cache des toolkits"""
        self._toolkit_cache.clear()
        self._logger.info("Toolkit cache cleared")
