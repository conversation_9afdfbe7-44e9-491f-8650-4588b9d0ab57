import { APIResource } from "../../resource.js";
import * as Core from "../../core.js";
import { OffsetPage, type OffsetPageParams } from "../../pagination.js";
export declare class Formatted extends APIResource {
    /**
     * Returns a page of tools from the engine configuration, optionally filtered by
     * toolkit, formatted for a specific provider
     */
    list(query?: FormattedListParams, options?: Core.RequestOptions): Core.PagePromise<FormattedListResponsesOffsetPage, FormattedListResponse>;
    list(options?: Core.RequestOptions): Core.PagePromise<FormattedListResponsesOffsetPage, FormattedListResponse>;
    /**
     * Returns the formatted tool specification for a specific tool, given a provider
     */
    get(name: string, query?: FormattedGetParams, options?: Core.RequestOptions): Core.APIPromise<unknown>;
    get(name: string, options?: Core.RequestOptions): Core.APIPromise<unknown>;
}
export declare class FormattedListResponsesOffsetPage extends OffsetPage<FormattedListResponse> {
}
export type FormattedListResponse = unknown;
export type FormattedGetResponse = unknown;
export interface FormattedListParams extends OffsetPageParams {
    /**
     * Provider format
     */
    format?: string;
    /**
     * Toolkit name
     */
    toolkit?: string;
    /**
     * User ID
     */
    user_id?: string;
}
export interface FormattedGetParams {
    /**
     * Provider format
     */
    format?: string;
    /**
     * User ID
     */
    user_id?: string;
}
export declare namespace Formatted {
    export { type FormattedListResponse as FormattedListResponse, type FormattedGetResponse as FormattedGetResponse, FormattedListResponsesOffsetPage as FormattedListResponsesOffsetPage, type FormattedListParams as FormattedListParams, type FormattedGetParams as FormattedGetParams, };
}
//# sourceMappingURL=formatted.d.ts.map