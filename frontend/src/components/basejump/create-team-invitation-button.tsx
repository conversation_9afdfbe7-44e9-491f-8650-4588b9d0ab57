'use client';

import { But<PERSON> } from '@/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import NewInvitationForm from './new-invitation-form';

type Props = {
  accountId: string;
};

export default function CreateTeamInvitationButton({ accountId }: Props) {
  return (
    <Dialog>
      <DialogTrigger asChild>
        <Button
          variant="outline"
          className="rounded-lg h-9 border-subtle dark:border-white/10 hover:bg-hover-bg dark:hover:bg-hover-bg-dark"
        >
          Invite un membre
        </Button>
      </DialogTrigger>
      <DialogContent className="sm:max-w-[425px] border-subtle dark:border-white/10 bg-card-bg dark:bg-background-secondary rounded-2xl shadow-custom">
        <DialogHeader>
          <DialogTitle className="text-card-title">
            Inviter un membre de l'équipe
          </DialogTitle>
          <DialogDescription className="text-foreground/70">
            Envoyer une invitation par e-mail pour rejoindre votre équipe
          </DialogDescription>
        </DialogHeader>
        <NewInvitationForm accountId={accountId} />
      </DialogContent>
    </Dialog>
  );
}
