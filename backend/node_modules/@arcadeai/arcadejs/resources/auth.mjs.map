{"version": 3, "file": "auth.mjs", "sourceRoot": "", "sources": ["../src/resources/auth.ts"], "names": [], "mappings": "AAAA,sFAAsF;OAE/E,EAAE,WAAW,EAAE;AAItB,MAAM,CAAC,MAAM,0BAA0B,GAAG,EAAE,CAAC;AAE7C;;GAEG;AACH,MAAM,OAAO,kBAAmB,SAAQ,KAAK;IAC3C,YAAY,OAAe;QACzB,KAAK,CAAC,OAAO,CAAC,CAAC;QACf,IAAI,CAAC,IAAI,GAAG,oBAAoB,CAAC;IACnC,CAAC;CACF;AAED,MAAM,OAAO,IAAK,SAAQ,WAAW;IACnC;;;;;;;;;;;;;OAaG;IACH,KAAK,CACH,MAAc,EACd,QAAgB,EAChB,UAA4B,EAAE;QAE9B,MAAM,EAAE,YAAY,GAAG,QAAQ,EAAE,MAAM,GAAG,EAAE,EAAE,GAAG,OAAO,CAAC;QAEzD,MAAM,eAAe,GAAwC;YAC3D,WAAW,EAAE,QAAQ;YACrB,aAAa,EAAE,YAAY;YAC3B,MAAM,EAAE;gBACN,MAAM;aACP;SACF,CAAC;QAEF,OAAO,IAAI,CAAC,SAAS,CAAC;YACpB,gBAAgB,EAAE,eAAe;YACjC,OAAO,EAAE,MAAM;SAChB,CAAC,CAAC;IACL,CAAC;IAED;;OAEG;IACH,SAAS,CACP,IAAyB,EACzB,OAA6B;QAE7B,OAAO,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,oBAAoB,EAAE,EAAE,IAAI,EAAE,GAAG,OAAO,EAAE,CAAC,CAAC;IACvE,CAAC;IAED;;OAEG;IACH,WAAW,CACT,IAA2B,EAC3B,OAA6B;QAE7B,OAAO,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,uBAAuB,EAAE,EAAE,IAAI,EAAE,GAAG,OAAO,EAAE,CAAC,CAAC;IAC1E,CAAC;IAED;;;;OAIG;IACH,MAAM,CACJ,KAAuB,EACvB,OAA6B;QAE7B,OAAO,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,iBAAiB,EAAE,EAAE,KAAK,EAAE,GAAG,OAAO,EAAE,CAAC,CAAC;IACpE,CAAC;IAED;;;;;;;;;;;;;;;;;;OAkBG;IACH,KAAK,CAAC,iBAAiB,CACrB,gBAAuD;QAEvD,IAAI,MAAc,CAAC;QACnB,IAAI,YAA0C,CAAC;QAE/C,IAAI,OAAO,gBAAgB,KAAK,QAAQ,EAAE;YACxC,MAAM,GAAG,gBAAgB,CAAC;YAC1B,YAAY,GAAG,EAAE,MAAM,EAAE,SAAS,EAAkC,CAAC;SACtE;aAAM;YACL,IAAI,CAAC,gBAAgB,CAAC,EAAE,EAAE;gBACxB,MAAM,IAAI,kBAAkB,CAAC,8BAA8B,CAAC,CAAC;aAC9D;YACD,MAAM,GAAG,gBAAgB,CAAC,EAAE,CAAC;YAC7B,YAAY,GAAG,gBAAgB,CAAC;SACjC;QAED,OAAO,YAAY,CAAC,MAAM,KAAK,WAAW,EAAE;YAC1C,YAAY,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC;gBAC/B,EAAE,EAAE,MAAM;gBACV,IAAI,EAAE,0BAA0B;aACjC,CAAC,CAAC;SACJ;QAED,OAAO,YAAY,CAAC;IACtB,CAAC;CACF"}