export interface AuthorizationContext {
    token?: string;
    user_info?: {
        [key: string]: unknown;
    };
}
export interface AuthorizationResponse {
    id?: string;
    context?: AuthorizationContext;
    provider_id?: string;
    scopes?: Array<string>;
    status?: 'not_started' | 'pending' | 'completed' | 'failed';
    url?: string;
    user_id?: string;
}
export interface Error {
    message?: string;
    name?: string;
}
//# sourceMappingURL=shared.d.ts.map