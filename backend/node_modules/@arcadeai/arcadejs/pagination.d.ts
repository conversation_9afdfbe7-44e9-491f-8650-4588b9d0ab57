import { AbstractPage, Response, APIClient, FinalRequestOptions, PageInfo } from "./core.js";
export interface OffsetPageResponse<Item> {
    items: Array<Item>;
    total_count: number;
    offset: number;
}
export interface OffsetPageParams {
    /**
     * The number of elements to skip.
     */
    offset?: number;
    /**
     * The maximum number of elements to fetch.
     */
    limit?: number;
}
export declare class OffsetPage<Item> extends AbstractPage<Item> implements OffsetPageResponse<Item> {
    items: Array<Item>;
    total_count: number;
    offset: number;
    constructor(client: APIClient, response: Response, body: OffsetPageResponse<Item>, options: FinalRequestOptions);
    getPaginatedItems(): Item[];
    nextPageParams(): Partial<OffsetPageParams> | null;
    nextPageInfo(): PageInfo | null;
}
//# sourceMappingURL=pagination.d.ts.map