from typing import List, Optional, Dict, Any
from ..domain.entities import Tool, ToolkitInfo
from ..domain.value_objects import UserId, ToolkitName
from ..support.arcade_client import ArcadeClient
from ..protocols import Logger


class ArcadeToolRepository:
    """
    Repository pour les outils Arcade utilisant l'API Arcade.dev
    """
    
    def __init__(self, arcade_client: ArcadeClient, logger: Logger):
        self._arcade_client = arcade_client
        self._logger = logger
    
    async def list_toolkits(self, category: Optional[str] = None) -> List[ToolkitInfo]:
        """
        Liste tous les toolkits disponibles
        """
        try:
            self._logger.info(f"Listing toolkits, category: {category}")
            
            response = await self._arcade_client.list_toolkits(category=category)
            
            toolkit_infos = []
            for toolkit_data in response.get("toolkits", []):
                toolkit_info = ToolkitInfo(
                    name=toolkit_data["name"],
                    display_name=toolkit_data["display_name"],
                    description=toolkit_data["description"],
                    category=toolkit_data["category"]
                )
                toolkit_infos.append(toolkit_info)
            
            self._logger.info(f"Found {len(toolkit_infos)} toolkits")
            return toolkit_infos
            
        except Exception as e:
            self._logger.error(f"Error listing toolkits: {str(e)}")
            return []
    
    async def get_toolkit_tools(self, toolkit_name: ToolkitName, user_id: UserId) -> List[Tool]:
        """
        Récupère les outils d'un toolkit spécifique
        """
        try:
            self._logger.info(f"Getting tools for toolkit {toolkit_name.value}")
            
            tools_data = await self._arcade_client.get_toolkit_tools(
                toolkit_name.value, 
                user_id.value
            )
            
            tools = []
            for tool_data in tools_data:
                tool = Tool(
                    name=tool_data["name"],
                    description=tool_data["description"],
                    toolkit_name=toolkit_name.value,
                    input_schema=tool_data.get("input_schema", {}),
                    requires_auth=tool_data.get("requires_auth", False)
                )
                tools.append(tool)
            
            self._logger.info(f"Found {len(tools)} tools for toolkit {toolkit_name.value}")
            return tools
            
        except Exception as e:
            self._logger.error(f"Error getting toolkit tools: {str(e)}")
            return []
    
    async def search_toolkits(
        self, 
        query: Optional[str] = None, 
        category: Optional[str] = None
    ) -> List[ToolkitInfo]:
        """
        Recherche dans les toolkits
        """
        try:
            self._logger.info(f"Searching toolkits with query: {query}, category: {category}")
            
            response = await self._arcade_client.list_toolkits(
                category=category,
                search=query
            )
            
            toolkit_infos = []
            for toolkit_data in response.get("toolkits", []):
                toolkit_info = ToolkitInfo(
                    name=toolkit_data["name"],
                    display_name=toolkit_data["display_name"],
                    description=toolkit_data["description"],
                    category=toolkit_data["category"]
                )
                toolkit_infos.append(toolkit_info)
            
            self._logger.info(f"Search returned {len(toolkit_infos)} toolkits")
            return toolkit_infos
            
        except Exception as e:
            self._logger.error(f"Error searching toolkits: {str(e)}")
            return []
    
    async def get_toolkit_by_name(self, toolkit_name: ToolkitName) -> Optional[ToolkitInfo]:
        """
        Récupère un toolkit par nom
        """
        try:
            # Lister tous les toolkits et trouver celui qui correspond
            all_toolkits = await self.list_toolkits()
            
            for toolkit in all_toolkits:
                if toolkit.name == toolkit_name.value:
                    return toolkit
            
            return None
            
        except Exception as e:
            self._logger.error(f"Error getting toolkit by name: {str(e)}")
            return None
    
    async def execute_tool(
        self, 
        tool_name: str, 
        user_id: UserId, 
        input_data: Dict[str, Any]
    ) -> Dict[str, Any]:
        """
        Exécute un outil
        """
        try:
            self._logger.info(f"Executing tool {tool_name} for user {user_id.value}")
            
            result = await self._arcade_client.execute_tool(
                tool_name, 
                user_id.value, 
                input_data
            )
            
            return result
            
        except Exception as e:
            self._logger.error(f"Error executing tool: {str(e)}")
            return {
                "success": False,
                "error": str(e),
                "tool_name": tool_name
            }
