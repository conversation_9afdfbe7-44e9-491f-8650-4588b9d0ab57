// File generated from our OpenAPI spec by Stainless. See CONTRIBUTING.md for details.
var _Arcade_instances, _a, _Arcade_baseURLOverridden;
import * as qs from "./internal/qs/index.mjs";
import * as Core from "./core.mjs";
import * as Errors from "./error.mjs";
import * as Pagination from "./pagination.mjs";
import * as Uploads from "./uploads.mjs";
import * as API from "./resources/index.mjs";
import { Auth, } from "./resources/auth.mjs";
import { Health } from "./resources/health.mjs";
import { WorkerResponsesOffsetPage, Workers, } from "./resources/workers.mjs";
import { Admin } from "./resources/admin/admin.mjs";
import { Chat } from "./resources/chat/chat.mjs";
import { ToolDefinitionsOffsetPage, Tools, } from "./resources/tools/tools.mjs";
/**
 * API Client for interfacing with the Arcade API.
 */
export class Arcade extends Core.APIClient {
    /**
     * API Client for interfacing with the Arcade API.
     *
     * @param {string | undefined} [opts.apiKey=process.env['ARCADE_API_KEY'] ?? undefined]
     * @param {string} [opts.baseURL=process.env['ARCADE_BASE_URL'] ?? https://api.arcade.dev] - Override the default base URL for the API.
     * @param {number} [opts.timeout=1 minute] - The maximum amount of time (in milliseconds) the client will wait for a response before timing out.
     * @param {number} [opts.httpAgent] - An HTTP agent used to manage HTTP(s) connections.
     * @param {Core.Fetch} [opts.fetch] - Specify a custom `fetch` function implementation.
     * @param {number} [opts.maxRetries=2] - The maximum number of times the client will retry a request.
     * @param {Core.Headers} opts.defaultHeaders - Default headers to include with every request to the API.
     * @param {Core.DefaultQuery} opts.defaultQuery - Default query parameters to include with every request to the API.
     */
    constructor({ baseURL = Core.readEnv('ARCADE_BASE_URL'), apiKey = Core.readEnv('ARCADE_API_KEY'), ...opts } = {}) {
        if (apiKey === undefined) {
            throw new Errors.ArcadeError("The ARCADE_API_KEY environment variable is missing or empty; either provide it, or instantiate the Arcade client with an apiKey option, like new Arcade({ apiKey: 'My API Key' }).");
        }
        const options = {
            apiKey,
            ...opts,
            baseURL: baseURL || `https://api.arcade.dev`,
        };
        super({
            baseURL: options.baseURL,
            baseURLOverridden: baseURL ? baseURL !== 'https://api.arcade.dev' : false,
            timeout: options.timeout ?? 60000 /* 1 minute */,
            httpAgent: options.httpAgent,
            maxRetries: options.maxRetries,
            fetch: options.fetch,
        });
        _Arcade_instances.add(this);
        this.admin = new API.Admin(this);
        this.auth = new API.Auth(this);
        this.health = new API.Health(this);
        this.chat = new API.Chat(this);
        this.tools = new API.Tools(this);
        this.workers = new API.Workers(this);
        this._options = options;
        this.idempotencyHeader = 'Idempotency-Key';
        this.apiKey = apiKey;
    }
    defaultQuery() {
        return this._options.defaultQuery;
    }
    defaultHeaders(opts) {
        return {
            ...super.defaultHeaders(opts),
            ...this._options.defaultHeaders,
        };
    }
    authHeaders(opts) {
        return { Authorization: this.apiKey };
    }
    stringifyQuery(query) {
        return qs.stringify(query, { arrayFormat: 'comma' });
    }
}
_a = Arcade, _Arcade_instances = new WeakSet(), _Arcade_baseURLOverridden = function _Arcade_baseURLOverridden() {
    return this.baseURL !== 'https://api.arcade.dev';
};
Arcade.Arcade = _a;
Arcade.DEFAULT_TIMEOUT = 60000; // 1 minute
Arcade.ArcadeError = Errors.ArcadeError;
Arcade.APIError = Errors.APIError;
Arcade.APIConnectionError = Errors.APIConnectionError;
Arcade.APIConnectionTimeoutError = Errors.APIConnectionTimeoutError;
Arcade.APIUserAbortError = Errors.APIUserAbortError;
Arcade.NotFoundError = Errors.NotFoundError;
Arcade.ConflictError = Errors.ConflictError;
Arcade.RateLimitError = Errors.RateLimitError;
Arcade.BadRequestError = Errors.BadRequestError;
Arcade.AuthenticationError = Errors.AuthenticationError;
Arcade.InternalServerError = Errors.InternalServerError;
Arcade.PermissionDeniedError = Errors.PermissionDeniedError;
Arcade.UnprocessableEntityError = Errors.UnprocessableEntityError;
Arcade.toFile = Uploads.toFile;
Arcade.fileFromPath = Uploads.fileFromPath;
Arcade.Admin = Admin;
Arcade.Auth = Auth;
Arcade.Health = Health;
Arcade.Chat = Chat;
Arcade.Tools = Tools;
Arcade.ToolDefinitionsOffsetPage = ToolDefinitionsOffsetPage;
Arcade.Workers = Workers;
Arcade.WorkerResponsesOffsetPage = WorkerResponsesOffsetPage;
export { toFile, fileFromPath } from "./uploads.mjs";
export { ArcadeError, APIError, APIConnectionError, APIConnectionTimeoutError, APIUserAbortError, NotFoundError, ConflictError, RateLimitError, BadRequestError, AuthenticationError, InternalServerError, PermissionDeniedError, UnprocessableEntityError, } from "./error.mjs";
export default Arcade;
//# sourceMappingURL=index.mjs.map