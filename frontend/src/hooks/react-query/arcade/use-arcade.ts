'use client';

import { useMutation, useQueryClient, useQuery } from '@tanstack/react-query';
import { backendApi } from '@/lib/api-client';
import { arcadeKeys } from './keys';

// Types compatibles avec Pipedream
export interface CreateConnectionTokenRequest {
  toolkit?: string;
}

export interface ConnectionTokenResponse {
  success: boolean;
  auth_url?: string;
  auth_id?: string;
  user_id: string;
  toolkit?: string;
  status?: string;
  error?: string;
}

export interface ConnectionResponse {
  success: boolean;
  connections: Connection[];
  count: number;
  error?: string;
}

export interface Connection {
  toolkit: string;
  app_name: string;
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

export interface ToolkitResponse {
  success: boolean;
  toolkits: ArcadeToolkit[];
  count: number;
  error?: string;
}

export interface ArcadeToolkit {
  name: string;
  display_name: string;
  description: string;
  category: string;
  tool_count: number;
}

export interface ToolDiscoveryResponse {
  success: boolean;
  tools: ArcadeTool[];
  count: number;
  error?: string;
}

export interface ArcadeTool {
  name: string;
  description: string;
  input_schema: any;
  toolkit: string;
  requires_auth: boolean;
}

// Hook pour créer un token de connexion (compatible Pipedream)
export const useCreateConnectionToken = () => {
  return useMutation({
    mutationFn: async (request: CreateConnectionTokenRequest = {}): Promise<ConnectionTokenResponse> => {
      const result = await backendApi.post<ConnectionTokenResponse>(
        '/arcade/connection-token',
        request,
        {
          errorContext: { operation: 'create connection token', resource: 'Arcade connection' },
        }
      );

      if (!result.success) {
        throw new Error(result.error?.message || 'Failed to create connection token');
      }

      return result.data!;
    },
  });
};

// Hook pour récupérer les connexions (compatible Pipedream)
export const useConnections = () => {
  return useQuery({
    queryKey: arcadeKeys.connections(),
    queryFn: async (): Promise<ConnectionResponse> => {
      const result = await backendApi.get<ConnectionResponse>(
        '/arcade/connections',
        {
          errorContext: { operation: 'get connections', resource: 'Arcade connections' },
        }
      );

      if (!result.success) {
        throw new Error(result.error?.message || 'Failed to get connections');
      }

      return result.data!;
    },
    staleTime: 5 * 60 * 1000, // 5 minutes
    retry: 2,
  });
};

// Hook pour rafraîchir les connexions
export const useRefreshConnections = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: async (): Promise<ConnectionResponse> => {
      const result = await backendApi.get<ConnectionResponse>('/arcade/connections');
      return result.data!;
    },
    onSuccess: (data) => {
      queryClient.setQueryData(arcadeKeys.connections(), data);
    },
    onError: (error) => {
      console.error('Failed to refresh connections:', error);
    },
  });
};

// Hook pour invalider les requêtes Arcade
export const useInvalidateArcadeQueries = () => {
  const queryClient = useQueryClient();
  return () => {
    queryClient.invalidateQueries({ queryKey: arcadeKeys.all });
  };
};

// Hook pour récupérer les toolkits (équivalent apps)
export const useArcadeToolkits = (search?: string, category?: string) => {
  return useQuery({
    queryKey: arcadeKeys.toolkits(search, category),
    queryFn: async (): Promise<ToolkitResponse> => {
      const params = new URLSearchParams();
      if (search) params.append('search', search);
      if (category) params.append('category', category);
      
      const url = `/arcade/toolkits${params.toString() ? `?${params.toString()}` : ''}`;
      const result = await backendApi.get<ToolkitResponse>(url, {
        errorContext: { operation: 'get toolkits', resource: 'Arcade toolkits' },
      });
      
      console.log('🔍 Toolkits:', result);
      return result.data!;
    },
    staleTime: 5 * 60 * 1000,
    retry: 2,
  });
};

// Hook pour récupérer les toolkits populaires
export const useArcadePopularToolkits = () => {
  return useQuery({
    queryKey: arcadeKeys.popularToolkits(),
    queryFn: async (): Promise<ToolkitResponse> => {
      const result = await backendApi.get<ToolkitResponse>('/arcade/toolkits/popular', {
        errorContext: { operation: 'get popular toolkits', resource: 'Arcade toolkits' },
      });
      
      console.log('🔍 Popular toolkits:', result);
      return result.data!;
    },
    staleTime: 30 * 60 * 1000,
    retry: 2,
  });
};

// Hook pour découvrir les outils (équivalent MCP discovery)
export const useArcadeToolDiscovery = (
  options: { toolkit_name?: string; oauth_app_id?: string } = {},
  enabled: boolean = true
) => {
  return useQuery({
    queryKey: arcadeKeys.toolDiscovery(options),
    queryFn: async (): Promise<ToolDiscoveryResponse> => {
      const response = await backendApi.post('/arcade/tools/discover', options);
      return response.data;
    },
    enabled,
    staleTime: 10 * 60 * 1000, // 10 minutes
    gcTime: 15 * 60 * 1000, // 15 minutes cache time
    refetchOnWindowFocus: false,
    refetchOnMount: false,
    retry: 2,
  });
};

// Hook pour connecter un toolkit (équivalent MCP connection)
export const useArcadeToolkitConnection = () => {
  return useMutation({
    mutationFn: async (request: { toolkit_name: string; oauth_app_id?: string }) => {
      const response = await backendApi.post('/arcade/tools/connect', request);
      return response.data;
    },
  });
};

// Hook pour découvrir les outils d'un toolkit spécifique
export const useArcadeToolDiscoveryForToolkit = (
  toolkit_name: string,
  oauth_app_id?: string,
  enabled: boolean = true
) => {
  return useArcadeToolDiscovery(
    { toolkit_name, oauth_app_id },
    enabled && !!toolkit_name
  );
};

// Hook pour récupérer tous les outils disponibles
export const useArcadeAvailableTools = () => {
  return useArcadeToolDiscovery({}, true);
};

// API object pour compatibilité avec le code existant
export const arcadeApi = {
  async createConnectionToken(request: CreateConnectionTokenRequest = {}): Promise<ConnectionTokenResponse> {
    const result = await backendApi.post<ConnectionTokenResponse>(
      '/arcade/connection-token',
      request,
      {
        errorContext: { operation: 'create connection token', resource: 'Arcade connection' },
      }
    );

    if (!result.success) {
      throw new Error(result.error?.message || 'Failed to create connection token');
    }

    return result.data!;
  },

  async getConnections(): Promise<ConnectionResponse> {
    const result = await backendApi.get<ConnectionResponse>(
      '/arcade/connections',
      {
        errorContext: { operation: 'get connections', resource: 'Arcade connections' },
      }
    );

    if (!result.success) {
      throw new Error(result.error?.message || 'Failed to get connections');
    }

    return result.data!;
  },

  async getToolkits(search?: string, category?: string): Promise<ToolkitResponse> {
    const params = new URLSearchParams();
    if (search) params.append('search', search);
    if (category) params.append('category', category);
    
    const url = `/arcade/toolkits${params.toString() ? `?${params.toString()}` : ''}`;
    const result = await backendApi.get<ToolkitResponse>(url, {
      errorContext: { operation: 'get toolkits', resource: 'Arcade toolkits' },
    });

    if (!result.success) {
      throw new Error(result.error?.message || 'Failed to get toolkits');
    }

    return result.data!;
  },

  async getPopularToolkits(): Promise<ToolkitResponse> {
    const result = await backendApi.get<ToolkitResponse>(
      '/arcade/toolkits/popular',
      {
        errorContext: { operation: 'get popular toolkits', resource: 'Arcade toolkits' },
      }
    );

    if (!result.success) {
      throw new Error(result.error?.message || 'Failed to get popular toolkits');
    }

    return result.data!;
  },

  async discoverTools(toolkit_name?: string): Promise<ToolDiscoveryResponse> {
    const request = { toolkit_name };
    
    const result = await backendApi.post<ToolDiscoveryResponse>(
      '/arcade/tools/discover',
      request,
      {
        errorContext: { operation: 'discover tools', resource: 'Arcade tools' },
      }
    );

    if (!result.success) {
      throw new Error(result.error?.message || 'Failed to discover tools');
    }

    return result.data!;
  },

  async connectToolkit(toolkit_name: string, oauth_app_id?: string) {
    const request = { toolkit_name, oauth_app_id };
    
    const result = await backendApi.post(
      '/arcade/tools/connect',
      request,
      {
        errorContext: { operation: 'connect toolkit', resource: 'Arcade toolkit' },
      }
    );

    if (!result.success) {
      throw new Error(result.error?.message || 'Failed to connect toolkit');
    }

    return result.data;
  },
};
