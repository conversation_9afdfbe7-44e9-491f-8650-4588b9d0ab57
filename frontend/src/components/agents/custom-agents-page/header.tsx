'use client';

import React from 'react';
import { Bot } from 'lucide-react';
import { PageHeader } from '@/components/ui/page-header';

export const AgentsPageHeader = () => {
  return (
    <PageHeader icon={Bot}>
      <div className="space-y-4">
        <div className="text-4xl font-semibold tracking-tight">
          <span className="text-primary">Employés IA</span> = <span className="text-primary">Employés IA</span>
        </div>
        <p className="text-lg text-muted-foreground max-w-2xl mx-auto leading-relaxed">
          Explore et crée tes propres agents personnalisés en combinant{' '}
          <span className="text-foreground font-medium">intégrations</span>,{' '}
          <span className="text-foreground font-medium">instructions</span>,{' '}
          <span className="text-foreground font-medium">connaissances</span>,{' '}
          <span className="text-foreground font-medium">triggers</span> and{' '}
          <span className="text-foreground font-medium">workflows</span>.
        </p>
      </div>
    </PageHeader>
  );
};
