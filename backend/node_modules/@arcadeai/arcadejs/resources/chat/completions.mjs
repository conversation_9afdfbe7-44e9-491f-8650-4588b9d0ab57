// File generated from our OpenAPI spec by Stainless. See CONTRIBUTING.md for details.
import { APIResource } from "../../resource.mjs";
export class Completions extends APIResource {
    /**
     * Talk to different LLM Chat APIs via OpenAI's API
     */
    create(body, options) {
        return this._client.post('/v1/chat/completions', { body, ...options });
    }
}
//# sourceMappingURL=completions.mjs.map