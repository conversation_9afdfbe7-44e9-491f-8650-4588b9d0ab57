// File generated from our OpenAPI spec by Stainless. See CONTRIBUTING.md for details.
import { AbstractPage } from "./core.mjs";
export class OffsetPage extends AbstractPage {
    constructor(client, response, body, options) {
        super(client, response, body, options);
        this.items = body.items || [];
        this.total_count = body.total_count || 0;
        this.offset = body.offset || 0;
    }
    getPaginatedItems() {
        return this.items ?? [];
    }
    // @deprecated Please use `nextPageInfo()` instead
    nextPageParams() {
        const info = this.nextPageInfo();
        if (!info)
            return null;
        if ('params' in info)
            return info.params;
        const params = Object.fromEntries(info.url.searchParams);
        if (!Object.keys(params).length)
            return null;
        return params;
    }
    nextPageInfo() {
        const offset = this.offset;
        if (!offset) {
            return null;
        }
        const length = this.getPaginatedItems().length;
        const currentCount = offset + length;
        const totalCount = this.total_count;
        if (!totalCount) {
            return null;
        }
        if (currentCount < totalCount) {
            return { params: { offset: currentCount } };
        }
        return null;
    }
}
//# sourceMappingURL=pagination.mjs.map