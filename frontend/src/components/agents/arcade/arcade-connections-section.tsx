'use client';

import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { 
  DropdownMenu, 
  DropdownMenuContent, 
  DropdownMenuItem, 
  DropdownMenuTrigger 
} from '@/components/ui/dropdown-menu';
import { 
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog';
import { 
  CheckCircle, 
  AlertCircle, 
  MoreVertical, 
  Trash2, 
  RefreshCw, 
  Settings,
  Loader2,
  Zap
} from 'lucide-react';
import { toast } from 'sonner';
import { useConnections, useRefreshConnections } from '@/hooks/react-query/arcade/use-arcade';
import type { Connection } from '@/hooks/react-query/arcade/use-arcade';

interface ArcadeConnectionsSectionProps {
  selectedAgentId?: string;
  className?: string;
}

export function ArcadeConnectionsSection({
  selectedAgentId,
  className
}: ArcadeConnectionsSectionProps) {
  const [connectionToDelete, setConnectionToDelete] = useState<Connection | null>(null);
  const [isDeleting, setIsDeleting] = useState(false);

  const { 
    data: connectionsData, 
    isLoading, 
    error,
    refetch 
  } = useConnections();

  const refreshConnectionsMutation = useRefreshConnections();

  const handleRefreshConnections = async () => {
    try {
      await refreshConnectionsMutation.mutateAsync();
      toast.success('Connections refreshed successfully');
    } catch (error) {
      toast.error('Failed to refresh connections');
    }
  };

  const handleDeleteConnection = async (connection: Connection) => {
    setIsDeleting(true);
    try {
      // TODO: Implémenter la suppression de connexion
      // await deleteConnectionMutation.mutateAsync(connection.toolkit);
      
      toast.success(`Disconnected from ${connection.app_name}`);
      setConnectionToDelete(null);
      refetch();
    } catch (error) {
      toast.error('Failed to disconnect');
    } finally {
      setIsDeleting(false);
    }
  };

  const getConnectionStatusIcon = (connection: Connection) => {
    if (connection.is_active) {
      return <CheckCircle className="h-4 w-4 text-green-500" />;
    } else {
      return <AlertCircle className="h-4 w-4 text-yellow-500" />;
    }
  };

  const getConnectionStatusText = (connection: Connection) => {
    return connection.is_active ? 'Connected' : 'Disconnected';
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  if (isLoading) {
    return (
      <Card className={className}>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Zap className="h-5 w-5" />
            Arcade Connections
          </CardTitle>
          <CardDescription>
            Manage your Arcade.dev toolkit connections
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center py-8">
            <Loader2 className="h-6 w-6 animate-spin" />
            <span className="ml-2">Loading connections...</span>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Card className={className}>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Zap className="h-5 w-5" />
            Arcade Connections
          </CardTitle>
          <CardDescription>
            Manage your Arcade.dev toolkit connections
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="text-center py-8">
            <AlertCircle className="h-8 w-8 text-red-500 mx-auto mb-2" />
            <p className="text-red-500 mb-4">Failed to load connections</p>
            <Button variant="outline" onClick={() => refetch()}>
              Try Again
            </Button>
          </div>
        </CardContent>
      </Card>
    );
  }

  const connections = connectionsData?.connections || [];

  return (
    <>
      <Card className={className}>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="flex items-center gap-2">
                <Zap className="h-5 w-5" />
                Arcade Connections
              </CardTitle>
              <CardDescription>
                Manage your Arcade.dev toolkit connections
              </CardDescription>
            </div>
            <div className="flex items-center gap-2">
              <Badge variant="secondary">
                {connections.length} connection{connections.length !== 1 ? 's' : ''}
              </Badge>
              <Button
                variant="outline"
                size="sm"
                onClick={handleRefreshConnections}
                disabled={refreshConnectionsMutation.isPending}
              >
                {refreshConnectionsMutation.isPending ? (
                  <Loader2 className="h-4 w-4 animate-spin" />
                ) : (
                  <RefreshCw className="h-4 w-4" />
                )}
              </Button>
            </div>
          </div>
        </CardHeader>

        <CardContent>
          {connections.length === 0 ? (
            <div className="text-center py-8">
              <Zap className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
              <h3 className="text-lg font-medium mb-2">No connections yet</h3>
              <p className="text-muted-foreground mb-4">
                Connect to Arcade toolkits to enable powerful integrations for your agents
              </p>
            </div>
          ) : (
            <div className="space-y-4">
              {connections.map((connection, index) => (
                <div key={`${connection.toolkit}-${index}`}>
                  <div className="flex items-center justify-between p-4 rounded-lg border">
                    <div className="flex items-center space-x-4">
                      <div className="w-10 h-10 rounded-lg bg-gradient-to-br from-blue-500 to-purple-600 flex items-center justify-center">
                        <span className="text-white font-semibold text-sm">
                          {connection.app_name.charAt(0).toUpperCase()}
                        </span>
                      </div>
                      
                      <div className="flex-1">
                        <div className="flex items-center gap-2">
                          <h4 className="font-medium">{connection.app_name}</h4>
                          <Badge variant="outline" className="text-xs">
                            {connection.toolkit}
                          </Badge>
                        </div>
                        <div className="flex items-center gap-4 text-sm text-muted-foreground mt-1">
                          <div className="flex items-center gap-1">
                            {getConnectionStatusIcon(connection)}
                            <span>{getConnectionStatusText(connection)}</span>
                          </div>
                          <span>•</span>
                          <span>Connected {formatDate(connection.created_at)}</span>
                        </div>
                      </div>
                    </div>

                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="ghost" size="sm">
                          <MoreVertical className="h-4 w-4" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        <DropdownMenuItem>
                          <Settings className="h-4 w-4 mr-2" />
                          Configure
                        </DropdownMenuItem>
                        <DropdownMenuItem>
                          <RefreshCw className="h-4 w-4 mr-2" />
                          Refresh
                        </DropdownMenuItem>
                        <Separator />
                        <DropdownMenuItem
                          className="text-red-600"
                          onClick={() => setConnectionToDelete(connection)}
                        >
                          <Trash2 className="h-4 w-4 mr-2" />
                          Disconnect
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </div>
                  
                  {index < connections.length - 1 && <Separator className="my-4" />}
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>

      {/* Dialog de confirmation de suppression */}
      <AlertDialog 
        open={!!connectionToDelete} 
        onOpenChange={() => setConnectionToDelete(null)}
      >
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Disconnect from {connectionToDelete?.app_name}?</AlertDialogTitle>
            <AlertDialogDescription>
              This will remove the connection to {connectionToDelete?.app_name}. 
              Any agents using this connection will lose access to its tools.
              You can reconnect at any time.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction
              onClick={() => connectionToDelete && handleDeleteConnection(connectionToDelete)}
              disabled={isDeleting}
              className="bg-red-600 hover:bg-red-700"
            >
              {isDeleting ? (
                <>
                  <Loader2 className="h-4 w-4 animate-spin mr-2" />
                  Disconnecting...
                </>
              ) : (
                'Disconnect'
              )}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  );
}
