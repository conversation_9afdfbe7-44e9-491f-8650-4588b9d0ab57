'use client';

import React from 'react';
import { <PERSON>, <PERSON>rkles, BarChart3 } from 'lucide-react';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@/components/ui/tooltip';
import { Badge } from '@/components/ui/badge';
import { cn } from '@/lib/utils';

interface AgentToggleSelectorProps {
  selectedAgentId?: string;
  onAgentSelect: (agentId: string) => void;
  onModelChange?: (modelId: string) => void;
  subscriptionStatus?: 'active' | 'no_subscription';
  className?: string;
}

const AGENTS = {
  'super-agent': {
    name: 'Super Agent',
    shortName: 'Super Agent',
    description: 'Agent polyvalent pour tous vos besoins quotidiens',
    capabilities: ['Rédaction', 'Analyse', 'Recherche', 'Conseil'],
    icon: Brain,
    color: 'aurora-green',
    isDefault: true,
    targetUsers: 'Idéal pour PME, cadres et étudiants',
    modelId: 'openrouter/google/gemini-2.5-flash',
    requiresSubscription: false
  },
  'agent-design': {
    name: 'Agent Créatif',
    shortName: 'Créatif',
    description: 'Spécialisé dans la création de contenu et génération d\'images',
    capabilities: ['Contenu', 'Marketing', 'Images', 'Communication'],
    icon: Sparkles,
    color: 'aurora-rainbow',
    isDefault: false,
    targetUsers: 'Parfait pour les équipes marketing et communication',
    modelId: 'openai/gpt-4.1',
    requiresSubscription: true
  },
  'agent-strategic': {
    name: 'Agent Analyste',
    shortName: 'Analyste',
    description: 'Expert en analyse de données et insights business',
    capabilities: ['Données', 'Tableaux', 'Rapports', 'KPI'],
    icon: BarChart3,
    color: 'aurora-blue',
    isDefault: false,
    targetUsers: 'Essentiel pour les décideurs et analystes',
    modelId: 'anthropic/claude-sonnet-4',
    requiresSubscription: true
  }
};

export function AgentToggleSelector({
  selectedAgentId = 'super-agent',
  onAgentSelect,
  onModelChange,
  subscriptionStatus = 'no_subscription',
  className
}: AgentToggleSelectorProps) {

  const handleAgentSelect = (agentId: string) => {
    // Check if user can access this agent
    const agent = AGENTS[agentId as keyof typeof AGENTS];
    const hasAccess = subscriptionStatus === 'active' || !agent?.requiresSubscription;

    if (!hasAccess) {
      // Don't allow selection of premium agents without subscription
      return;
    }

    onAgentSelect(agentId);
    // Trigger model change with the mapped model ID
    if (onModelChange && agent?.modelId) {
      onModelChange(agent.modelId);
    }
  };

  return (
    <div className={cn('flex items-center', className)}>
      {/* Container with subtle background and border */}
      <div className="flex items-center gap-1 p-1 rounded-full bg-gray-50/80 dark:bg-gray-900/50 border border-gray-200/50 dark:border-gray-700/50 backdrop-blur-sm">
        {Object.entries(AGENTS).map(([agentId, agent]) => {
          const isSelected = selectedAgentId === agentId;
          const IconComponent = agent.icon;
          const hasAccess = subscriptionStatus === 'active' || !agent.requiresSubscription;
          const isDisabled = !hasAccess;

          return (
            <TooltipProvider key={agentId}>
              <Tooltip>
                <TooltipTrigger asChild>
                  <button
                    onClick={() => handleAgentSelect(agentId)}
                    disabled={isDisabled}
                    className={cn(
                      'relative flex items-center gap-1.5 px-3 py-1.5 rounded-full text-xs font-medium transition-all duration-300 ease-out',
                      'focus:outline-none focus:ring-2 focus:ring-offset-1',
                      // Enabled states
                      !isDisabled && 'hover:scale-105 active:scale-95',
                      // Aurora Green - Super Agent
                      !isDisabled && isSelected && agent.color === 'aurora-green' && 'bg-gradient-to-r from-emerald-300/80 via-green-300/80 to-teal-300/80 text-emerald-800 shadow-lg shadow-emerald-200/50 dark:from-emerald-400/60 dark:via-green-400/60 dark:to-teal-400/60 dark:text-emerald-100 dark:shadow-emerald-900/30',
                      // Aurora Rainbow - Creative Agent
                      !isDisabled && isSelected && agent.color === 'aurora-rainbow' && 'bg-gradient-to-r from-pink-300/80 via-purple-300/80 via-blue-300/80 to-cyan-300/80 text-purple-800 shadow-lg shadow-purple-200/50 dark:from-pink-400/60 dark:via-purple-400/60 dark:via-blue-400/60 dark:to-cyan-400/60 dark:text-purple-100 dark:shadow-purple-900/30',
                      // Aurora Blue - Strategy Agent
                      !isDisabled && isSelected && agent.color === 'aurora-blue' && 'bg-gradient-to-r from-blue-300/80 via-indigo-300/80 to-purple-300/80 text-blue-800 shadow-lg shadow-blue-200/50 dark:from-blue-400/60 dark:via-indigo-400/60 dark:to-purple-400/60 dark:text-blue-100 dark:shadow-blue-900/30',
                      // Non-selected enabled state
                      !isDisabled && !isSelected && 'bg-white/60 text-gray-600 hover:bg-white/80 dark:bg-gray-800/60 dark:text-gray-400 dark:hover:bg-gray-800/80 border border-gray-200/30 dark:border-gray-700/30',
                      // Disabled state (premium agents without subscription)
                      isDisabled && 'bg-gray-100/50 text-gray-400 cursor-not-allowed opacity-50 dark:bg-gray-900/50 dark:text-gray-600 border border-gray-200/20 dark:border-gray-800/20',
                      // Focus states with aurora colors
                      !isDisabled && isSelected && agent.color === 'aurora-green' && 'focus:ring-emerald-300/50',
                      !isDisabled && isSelected && agent.color === 'aurora-rainbow' && 'focus:ring-purple-300/50',
                      !isDisabled && isSelected && agent.color === 'aurora-blue' && 'focus:ring-blue-300/50',
                      !isDisabled && !isSelected && 'focus:ring-gray-300/50'
                    )}
                  >
                    <IconComponent className="h-3 w-3" />
                    <span className="whitespace-nowrap">{agent.shortName}</span>

                    {agent.isDefault && isSelected && (
                      <div className="absolute -top-0.5 -right-0.5 w-2 h-2 bg-white rounded-full border border-emerald-400 shadow-sm" />
                    )}
                  </button>
                </TooltipTrigger>
              <TooltipContent side="top" className="max-w-xs">
                <div className="space-y-2">
                  <div className="flex items-center gap-2">
                    <IconComponent className="h-4 w-4" />
                    <span className="font-semibold">{agent.name}</span>
                    {agent.isDefault && (
                      <Badge variant="secondary" className="text-xs px-1.5 py-0">
                        Recommandé
                      </Badge>
                    )}
                    {agent.requiresSubscription && subscriptionStatus !== 'active' && (
                      <Badge variant="outline" className="text-xs px-1.5 py-0 text-amber-600 border-amber-300">
                        Premium
                      </Badge>
                    )}
                  </div>
                  <p className="text-sm text-muted-foreground">{agent.description}</p>
                  <div className="space-y-1">
                    <p className="text-xs font-medium">Capacités :</p>
                    <div className="flex flex-wrap gap-1">
                      {agent.capabilities.map((capability) => (
                        <Badge
                          key={capability}
                          variant="secondary"
                          className="text-xs px-2 py-0.5 bg-gray-100 dark:bg-gray-800 text-gray-700 dark:text-gray-300 border border-gray-200 dark:border-gray-700"
                        >
                          {capability}
                        </Badge>
                      ))}
                    </div>
                  </div>
                  <p className="text-xs text-muted-foreground italic">{agent.targetUsers}</p>
                  {agent.requiresSubscription && subscriptionStatus !== 'active' && (
                    <p className="text-xs text-amber-600 font-medium">
                      🔒 Abonnement requis pour accéder à cet agent
                    </p>
                  )}
                </div>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>
        );
      })}
      </div>
    </div>
  );
}
