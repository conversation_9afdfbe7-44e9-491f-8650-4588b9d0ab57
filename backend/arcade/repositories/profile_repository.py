from typing import List, Optional
from uuid import UUID, uuid4
from datetime import datetime

from ..domain.entities import Profile
from ..domain.value_objects import UserId
from ..domain.exceptions import ProfileNotFoundError, ProfileAlreadyExistsError
from ..protocols import DatabaseConnection, Logger


class SupabaseProfileRepository:
    """
    Repository pour les profils Arcade utilisant Supabase
    Compatible avec la structure existante
    """
    
    def __init__(self, db: DatabaseConnection, logger: Logger):
        self._db = db
        self._logger = logger
        self._table_name = "arcade_profiles"  # Nouvelle table pour les profils Arcade
    
    async def create(self, profile: Profile) -> Profile:
        """Crée un nouveau profil"""
        try:
            client = await self._db.client
            
            # Vérifier si un profil avec le même nom existe déjà
            existing = await client.table(self._table_name).select("*").eq(
                "account_id", str(profile.account_id)
            ).eq("profile_name", profile.profile_name).eq(
                "toolkit_name", profile.toolkit_name
            ).execute()
            
            if existing.data:
                raise ProfileAlreadyExistsError(
                    f"Profile {profile.profile_name} already exists for toolkit {profile.toolkit_name}"
                )
            
            # Insérer le nouveau profil
            profile_data = {
                "profile_id": str(profile.profile_id),
                "account_id": str(profile.account_id),
                "profile_name": profile.profile_name,
                "toolkit_name": profile.toolkit_name,
                "app_name": profile.app_name,
                "description": profile.description,
                "is_active": profile.is_active,
                "is_default": profile.is_default,
                "enabled_tools": profile.enabled_tools,
                "external_user_id": profile.external_user_id,
                "oauth_app_id": profile.oauth_app_id,
                "created_at": profile.created_at.isoformat(),
                "updated_at": profile.updated_at.isoformat()
            }
            
            result = await client.table(self._table_name).insert(profile_data).execute()
            
            if not result.data:
                raise Exception("Failed to create profile")
            
            self._logger.info(f"Created Arcade profile: {profile.profile_name} for toolkit {profile.toolkit_name}")
            return profile
            
        except ProfileAlreadyExistsError:
            raise
        except Exception as e:
            self._logger.error(f"Error creating profile: {str(e)}")
            raise
    
    async def get_by_id(self, account_id: UUID, profile_id: str) -> Optional[Profile]:
        """Récupère un profil par ID"""
        try:
            client = await self._db.client
            
            result = await client.table(self._table_name).select("*").eq(
                "account_id", str(account_id)
            ).eq("profile_id", profile_id).execute()
            
            if not result.data:
                return None
            
            return self._map_to_profile(result.data[0])
            
        except Exception as e:
            self._logger.error(f"Error getting profile by ID: {str(e)}")
            return None
    
    async def get_by_account(
        self, 
        account_id: UUID, 
        toolkit_name: Optional[str] = None,
        is_active: Optional[bool] = None
    ) -> List[Profile]:
        """Récupère les profils d'un compte"""
        try:
            client = await self._db.client
            
            query = client.table(self._table_name).select("*").eq("account_id", str(account_id))
            
            if toolkit_name:
                query = query.eq("toolkit_name", toolkit_name)
            
            if is_active is not None:
                query = query.eq("is_active", is_active)
            
            result = await query.execute()
            
            profiles = []
            for row in result.data:
                profile = self._map_to_profile(row)
                if profile:
                    profiles.append(profile)
            
            return profiles
            
        except Exception as e:
            self._logger.error(f"Error getting profiles by account: {str(e)}")
            return []
    
    async def update(self, profile: Profile) -> Profile:
        """Met à jour un profil"""
        try:
            client = await self._db.client
            
            profile.updated_at = datetime.utcnow()
            
            update_data = {
                "profile_name": profile.profile_name,
                "app_name": profile.app_name,
                "description": profile.description,
                "is_active": profile.is_active,
                "is_default": profile.is_default,
                "enabled_tools": profile.enabled_tools,
                "updated_at": profile.updated_at.isoformat()
            }
            
            result = await client.table(self._table_name).update(update_data).eq(
                "account_id", str(profile.account_id)
            ).eq("profile_id", str(profile.profile_id)).execute()
            
            if not result.data:
                raise ProfileNotFoundError(f"Profile {profile.profile_id} not found")
            
            self._logger.info(f"Updated Arcade profile: {profile.profile_name}")
            return profile
            
        except ProfileNotFoundError:
            raise
        except Exception as e:
            self._logger.error(f"Error updating profile: {str(e)}")
            raise
    
    async def delete(self, account_id: UUID, profile_id: str) -> bool:
        """Supprime un profil"""
        try:
            client = await self._db.client
            
            result = await client.table(self._table_name).delete().eq(
                "account_id", str(account_id)
            ).eq("profile_id", profile_id).execute()
            
            success = bool(result.data)
            
            if success:
                self._logger.info(f"Deleted Arcade profile: {profile_id}")
            
            return success
            
        except Exception as e:
            self._logger.error(f"Error deleting profile: {str(e)}")
            return False
    
    def _map_to_profile(self, row: dict) -> Optional[Profile]:
        """Mappe une ligne de base de données vers un objet Profile"""
        try:
            return Profile(
                profile_id=UUID(row["profile_id"]),
                account_id=UUID(row["account_id"]),
                profile_name=row["profile_name"],
                toolkit_name=row["toolkit_name"],
                app_name=row["app_name"],
                description=row.get("description"),
                is_active=row.get("is_active", True),
                is_default=row.get("is_default", False),
                enabled_tools=row.get("enabled_tools", []),
                external_user_id=row.get("external_user_id"),
                oauth_app_id=row.get("oauth_app_id"),
                created_at=datetime.fromisoformat(row["created_at"]) if row.get("created_at") else datetime.utcnow(),
                updated_at=datetime.fromisoformat(row["updated_at"]) if row.get("updated_at") else datetime.utcnow()
            )
        except Exception as e:
            self._logger.error(f"Error mapping profile: {str(e)}")
            return None
