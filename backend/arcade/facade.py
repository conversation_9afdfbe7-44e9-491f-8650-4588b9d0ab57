import logging
from typing import List, Optional, Dict, Any, Union
from uuid import UUID
from arcadepy import Arcade

from .domain.entities import Profile, Connection, Tool, ToolkitInfo
from .domain.value_objects import UserId, ToolkitName
from .repositories.profile_repository import SupabaseProfileRepository
from .repositories.tool_repository import ArcadeToolRepository
from .services.profile_service import ProfileService
from .services.auth_service import AuthService
from .services.tool_service import ToolService
from .support.arcade_client import ArcadeClient
from .protocols import DatabaseConnection, Logger


class ArcadeManager:
    """
    Facade principal pour l'intégration Arcade.dev
    Remplace PipedreamManager avec une API compatible
    """
    
    def __init__(
        self,
        db: Optional[DatabaseConnection] = None,
        logger: Optional[Logger] = None
    ):
        self._logger = logger or logging.getLogger(__name__)
        
        if db is None:
            from services.supabase import DBConnection
            self._db = DBConnection()
        else:
            self._db = db
        
        # Initialize Arcade client
        self._arcade_client = ArcadeClient()
        
        # Initialize repositories
        self._profile_repo = SupabaseProfileRepository(self._db, self._logger)
        self._tool_repo = ArcadeToolRepository(self._arcade_client, self._logger)
        
        # Initialize services
        self._auth_service = AuthService(self._arcade_client, self._logger)
        self._profile_service = ProfileService(
            self._profile_repo,
            self._auth_service,
            self._logger
        )
        self._tool_service = ToolService(
            self._tool_repo,
            self._arcade_client,
            self._logger
        )

    async def create_profile(
        self,
        account_id: str,
        profile_name: str,
        toolkit_name: str,
        app_name: str,
        description: Optional[str] = None,
        is_default: bool = False,
        oauth_app_id: Optional[str] = None,
        enabled_tools: Optional[List[str]] = None,
        external_user_id: Optional[str] = None
    ) -> Profile:
        """
        Crée un profil Arcade (équivalent à create_profile Pipedream)
        """
        return await self._profile_service.create_profile(
            UUID(account_id),
            profile_name,
            toolkit_name,
            app_name,
            description,
            is_default,
            oauth_app_id,
            enabled_tools,
            external_user_id
        )

    async def get_profile(self, account_id: str, profile_id: str) -> Optional[Profile]:
        """Récupère un profil par ID"""
        return await self._profile_service.get_profile(UUID(account_id), UUID(profile_id))

    async def get_profiles(
        self,
        account_id: str,
        toolkit_name: Optional[str] = None,
        is_active: Optional[bool] = None
    ) -> List[Profile]:
        """Liste les profils d'un utilisateur"""
        return await self._profile_service.get_profiles(UUID(account_id), toolkit_name, is_active)

    async def update_profile(
        self,
        account_id: str,
        profile_id: str,
        profile_name: Optional[str] = None,
        display_name: Optional[str] = None,
        is_active: Optional[bool] = None,
        is_default: Optional[bool] = None,
        enabled_tools: Optional[List[str]] = None
    ) -> Profile:
        """Met à jour un profil"""
        return await self._profile_service.update_profile(
            UUID(account_id),
            UUID(profile_id),
            profile_name,
            display_name,
            is_active,
            is_default,
            enabled_tools
        )

    async def delete_profile(self, account_id: str, profile_id: str) -> bool:
        """Supprime un profil"""
        return await self._profile_service.delete_profile(UUID(account_id), UUID(profile_id))

    async def create_connection_token(
        self,
        user_id: Union[str, UserId],
        toolkit: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        Crée un token de connexion Arcade (équivalent Pipedream)
        """
        if isinstance(user_id, str):
            user_id = UserId(user_id)
            
        return await self._auth_service.create_auth_flow(user_id, toolkit)

    async def get_connections(self, user_id: Union[str, UserId]) -> List[Connection]:
        """Récupère les connexions d'un utilisateur"""
        if isinstance(user_id, str):
            user_id = UserId(user_id)
            
        return await self._auth_service.get_user_connections(user_id)

    async def discover_available_tools(
        self,
        user_id: Union[str, UserId],
        toolkit_name: Optional[Union[str, ToolkitName]] = None
    ) -> List[Tool]:
        """
        Découvre les outils disponibles (équivalent discover_mcp_servers)
        """
        if isinstance(user_id, str):
            user_id = UserId(user_id)
        if isinstance(toolkit_name, str):
            toolkit_name = ToolkitName(toolkit_name)
            
        return await self._tool_service.discover_tools(user_id, toolkit_name)

    async def create_tool_connection(
        self,
        user_id: Union[str, UserId],
        toolkit_name: str,
        oauth_app_id: Optional[str] = None
    ) -> ToolkitInfo:
        """
        Crée une connexion d'outils (équivalent create_mcp_connection)
        """
        if isinstance(user_id, str):
            user_id = UserId(user_id)
            
        toolkit_name_vo = ToolkitName(toolkit_name)
        return await self._tool_service.create_toolkit_connection(user_id, toolkit_name_vo, oauth_app_id)

    async def search_toolkits(
        self,
        query: Optional[str] = None,
        category: Optional[str] = None,
        page: int = 1,
        limit: int = 20
    ) -> Dict[str, Any]:
        """Recherche dans les toolkits disponibles (équivalent search_apps)"""
        return await self._tool_service.search_toolkits(query, category, page, limit)

    async def get_toolkit_by_name(self, toolkit_name: str) -> Optional[ToolkitInfo]:
        """Récupère un toolkit par nom (équivalent get_app_by_slug)"""
        toolkit_name_vo = ToolkitName(toolkit_name)
        return await self._tool_service.get_toolkit_by_name(toolkit_name_vo)

    async def get_popular_toolkits(self, category: Optional[str] = None, limit: int = 100) -> List[ToolkitInfo]:
        """Récupère les toolkits populaires (équivalent get_popular_apps)"""
        return await self._tool_service.get_popular_toolkits(category, limit)

    async def get_enabled_tools_for_agent_profile(
        self,
        agent_id: str,
        profile_id: str,
        user_id: str
    ) -> List[str]:
        """
        Récupère les outils activés pour un profil d'agent
        Compatible avec l'API Pipedream existante
        """
        return await self._profile_service.get_enabled_tools_for_agent_profile(
            agent_id, profile_id, user_id
        )

    async def update_agent_profile_tools(
        self,
        agent_id: str,
        profile_id: str,
        user_id: str,
        enabled_tools: List[str]
    ) -> Dict[str, Any]:
        """
        Met à jour les outils d'un profil d'agent
        Compatible avec l'API Pipedream existante
        """
        return await self._profile_service.update_agent_profile_tools(
            agent_id, profile_id, user_id, enabled_tools
        )

    async def close(self):
        """Ferme les connexions"""
        await self._arcade_client.close()

    async def __aenter__(self):
        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        await self.close()
