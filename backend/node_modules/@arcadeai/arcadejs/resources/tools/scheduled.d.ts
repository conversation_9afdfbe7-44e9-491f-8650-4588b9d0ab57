import { APIResource } from "../../resource.js";
import * as Core from "../../core.js";
import * as ToolsAPI from "./tools.js";
import { ToolExecutionsOffsetPage } from "./tools.js";
import { type OffsetPageParams } from "../../pagination.js";
export declare class Scheduled extends APIResource {
    /**
     * Returns a page of scheduled tool executions
     */
    list(query?: ScheduledListParams, options?: Core.RequestOptions): Core.PagePromise<ToolExecutionsOffsetPage, ToolsAPI.ToolExecution>;
    list(options?: Core.RequestOptions): Core.PagePromise<ToolExecutionsOffsetPage, ToolsAPI.ToolExecution>;
    /**
     * Returns the details for a specific scheduled tool execution
     */
    get(id: string, options?: Core.RequestOptions): Core.APIPromise<ScheduledGetResponse>;
}
export interface ScheduledGetResponse {
    id?: string;
    attempts?: Array<ToolsAPI.ToolExecutionAttempt>;
    created_at?: string;
    execution_status?: string;
    execution_type?: string;
    finished_at?: string;
    input?: {
        [key: string]: unknown;
    };
    run_at?: string;
    started_at?: string;
    tool_name?: string;
    toolkit_name?: string;
    toolkit_version?: string;
    updated_at?: string;
    user_id?: string;
}
export interface ScheduledListParams extends OffsetPageParams {
}
export declare namespace Scheduled {
    export { type ScheduledGetResponse as ScheduledGetResponse, type ScheduledListParams as ScheduledListParams, };
}
export { ToolExecutionsOffsetPage };
//# sourceMappingURL=scheduled.d.ts.map