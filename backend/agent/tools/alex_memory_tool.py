import logging
from typing import Dict, List
from agentpress.tool import Too<PERSON>, ToolResult, ToolSchema, SchemaType
from services.supabase import DBConnection

logger = logging.getLogger(__name__)


class AlexMemoryTool(Tool):
    def __init__(self, thread_id: str = None, account_id: str = None):
        super().__init__()
        self.thread_id = thread_id
        self.account_id = account_id
        self.db = DBConnection()

    def get_schemas(self) -> Dict[str, List[ToolSchema]]:
        return {
            "remember": [
                ToolSchema(
                    schema_type=SchemaType.OPENAPI,
                    schema={
                        "type": "function",
                        "function": {
                            "name": "remember",
                            "description": "Save information about the user",
                            "parameters": {
                                "type": "object",
                                "properties": {
                                    "title": {
                                        "type": "string",
                                        "description": "Title of the memory"
                                    },
                                    "content": {
                                        "type": "string",
                                        "description": "Content of the memory"
                                    },
                                    "memory_type": {
                                        "type": "string",
                                        "description": "Optional type",
                                        "default": "context"
                                    },
                                    "importance": {
                                        "type": "integer",
                                        "default": 3,
                                        "description": "1-5, bigger means more important",
                                        "minimum": 1,
                                        "maximum": 5,
                                    }
                                },
                                "required": ["title", "content"]
                            }
                        }
                    }
                )
            ],
            "get_user_context": [
                ToolSchema(
                    schema_type=SchemaType.OPENAPI,
                    schema={
                        "type": "function",
                        "function": {
                            "name": "get_user_context",
                            "description": "Retrieve user context",
                            "parameters": {
                                "type": "object",
                                "properties": {},
                                "required": []
                            }
                        }
                    }
                )
            ]
        }

    async def remember(self, title: str, content: str, memory_type: str = "context", importance = 3) -> ToolResult:
        try:
            if not self.account_id:
                return self.success_response("Memory noted (guest mode)")

            client = await self.db.client
            await client.table('alex_memories').insert({
                'account_id': self.account_id,
                'memory_type': memory_type,
                'title': title,
                'content': content,
                'importance': importance,
                'status': 'active'
            }).execute()

            return self.success_response(f"Memory saved: {title}")

        except Exception as e:
            logger.warning(f"Memory error (non-blocking): {e}")
            # NEVER fail - always return success
            return self.success_response(f"Memory noted: {title}")

    async def get_user_context(self) -> ToolResult:
        try:
            if not self.account_id:
                return self.success_response("First time we've spoken!")

            client = await self.db.client
            result = await client.table('alex_memories') \
                .select('*') \
                .eq('account_id', self.account_id) \
                .eq('status', 'active') \
                .order('created_at', desc=True) \
                .limit(10) \
                .execute()

            if not result.data:
                return self.success_response("No user context yet.")

            context_text = "What I know about you:\n\n"
            for memory in result.data:
                context_text += f"• {memory['title']}: {memory['content']}\n"

            return self.success_response(context_text)

        except Exception as e:
            logger.warning(f"Context error (non-blocking): {e}")
            # NEVER fail - always return success
            return self.success_response("Context temporarily unavailable.")
