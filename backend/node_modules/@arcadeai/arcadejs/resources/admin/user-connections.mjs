// File generated from our OpenAPI spec by Stainless. See CONTRIBUTING.md for details.
import { APIResource } from "../../resource.mjs";
import { isRequestOptions } from "../../core.mjs";
import { OffsetPage } from "../../pagination.mjs";
export class UserConnections extends APIResource {
    list(query = {}, options) {
        if (isRequestOptions(query)) {
            return this.list({}, query);
        }
        return this._client.getAPIList('/v1/admin/user_connections', UserConnectionResponsesOffsetPage, {
            query,
            ...options,
        });
    }
    /**
     * Delete a user/auth provider connection
     */
    delete(id, options) {
        return this._client.delete(`/v1/admin/user_connections/${id}`, {
            ...options,
            headers: { Accept: '*/*', ...options?.headers },
        });
    }
}
export class UserConnectionResponsesOffsetPage extends OffsetPage {
}
UserConnections.UserConnectionResponsesOffsetPage = UserConnectionResponsesOffsetPage;
//# sourceMappingURL=user-connections.mjs.map