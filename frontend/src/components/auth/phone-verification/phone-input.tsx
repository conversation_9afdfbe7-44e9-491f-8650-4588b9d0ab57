"use client";

import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Loader2, Phone } from "lucide-react";
import { PhoneInput as PhoneInputComponent } from "@/components/ui/phone-input";

interface PhoneInputFormProps {
  onSubmit: (phoneNumber: string) => Promise<void>;
  isLoading?: boolean;
  error?: string | null;
}

export function PhoneInput({ onSubmit, isLoading = false, error = null }: PhoneInputFormProps) {
  const [phoneNumber, setPhoneNumber] = useState("");
  const [localError, setLocalError] = useState<string | null>(null);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLocalError(null);

    // Basic validation
    if (!phoneNumber.trim()) {
      setLocalError("Veuillez entrer un numéro de téléphone");
      return;
    }

    // Simple phone number validation (international format)
    const phoneRegex = /^\+?[1-9]\d{1,14}$/;
    if (!phoneRegex.test(phoneNumber.replace(/\s/g, ""))) {
      setLocalError("Veuillez entrer un numéro de téléphone valide");
      return;
    }

    await onSubmit(phoneNumber);
  };

  return (
    <Card className="w-full max-w-md mx-auto">
      <CardHeader>
        <CardTitle>Vérification du téléphone</CardTitle>
        <CardDescription>
          Entrez votre numéro de téléphone pour recevoir un code de vérification par SMS
        </CardDescription>
      </CardHeader>
      <CardContent>
        <form onSubmit={handleSubmit} className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="phone">Numéro de téléphone</Label>
            <PhoneInputComponent
              value={phoneNumber}
              onChange={(value) => setPhoneNumber(value || "")}
              defaultCountry="US"
              placeholder="Entrez votre numéro de téléphone"
              disabled={isLoading}
            />
            <p className="text-sm text-muted-foreground">
              Nous vous enverrons un code de vérification pour confirmer votre numéro
            </p>
          </div>

          {(error || localError) && (
            <Alert variant="destructive">
              <AlertDescription>
                {error || localError}
              </AlertDescription>
            </Alert>
          )}

          <Button 
            type="submit" 
            className="w-full" 
            disabled={isLoading || !phoneNumber.trim()}
          >
            {isLoading ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Envoi du code...
              </>
            ) : (
              <>
                <Phone className="mr-2 h-4 w-4" />
                Envoyer le code de vérification
              </>
            )}
          </Button>
        </form>
      </CardContent>
    </Card>
  );
}