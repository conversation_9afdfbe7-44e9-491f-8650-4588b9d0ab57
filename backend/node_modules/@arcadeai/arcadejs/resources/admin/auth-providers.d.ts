import { APIResource } from "../../resource.js";
import * as Core from "../../core.js";
export declare class AuthProviders extends APIResource {
    /**
     * Create a new auth provider
     */
    create(body: AuthProviderCreateParams, options?: Core.RequestOptions): Core.APIPromise<AuthProviderResponse>;
    /**
     * List a page of auth providers that are available to the caller
     */
    list(options?: Core.RequestOptions): Core.APIPromise<AuthProviderListResponse>;
    /**
     * Delete a specific auth provider
     */
    delete(id: string, options?: Core.RequestOptions): Core.APIPromise<AuthProviderResponse>;
    /**
     * Get the details of a specific auth provider
     */
    get(id: string, options?: Core.RequestOptions): Core.APIPromise<AuthProviderResponse>;
    /**
     * Patch an existing auth provider
     */
    patch(pathId: string, body: AuthProviderPatchParams, options?: Core.RequestOptions): Core.APIPromise<AuthProviderResponse>;
}
export interface AuthProviderCreateRequest {
    id: string;
    description?: string;
    oauth2?: AuthProviderCreateRequest.Oauth2;
    provider_id?: string;
    status?: string;
    type?: string;
}
export declare namespace AuthProviderCreateRequest {
    interface Oauth2 {
        client_id: string;
        authorize_request?: Oauth2.AuthorizeRequest;
        client_secret?: string;
        pkce?: Oauth2.Pkce;
        refresh_request?: Oauth2.RefreshRequest;
        scope_delimiter?: ',' | ' ';
        token_introspection_request?: Oauth2.TokenIntrospectionRequest;
        token_request?: Oauth2.TokenRequest;
        user_info_request?: Oauth2.UserInfoRequest;
    }
    namespace Oauth2 {
        interface AuthorizeRequest {
            endpoint: string;
            auth_method?: string;
            method?: string;
            params?: {
                [key: string]: string;
            };
            request_content_type?: 'application/x-www-form-urlencoded' | 'application/json';
            response_content_type?: 'application/x-www-form-urlencoded' | 'application/json';
            response_map?: {
                [key: string]: string;
            };
        }
        interface Pkce {
            code_challenge_method?: string;
            enabled?: boolean;
        }
        interface RefreshRequest {
            endpoint: string;
            auth_method?: string;
            method?: string;
            params?: {
                [key: string]: string;
            };
            request_content_type?: 'application/x-www-form-urlencoded' | 'application/json';
            response_content_type?: 'application/x-www-form-urlencoded' | 'application/json';
            response_map?: {
                [key: string]: string;
            };
        }
        interface TokenIntrospectionRequest {
            endpoint: string;
            triggers: TokenIntrospectionRequest.Triggers;
            auth_method?: string;
            method?: string;
            params?: {
                [key: string]: string;
            };
            request_content_type?: 'application/x-www-form-urlencoded' | 'application/json';
            response_content_type?: 'application/x-www-form-urlencoded' | 'application/json';
            response_map?: {
                [key: string]: string;
            };
        }
        namespace TokenIntrospectionRequest {
            interface Triggers {
                on_token_grant?: boolean;
                on_token_refresh?: boolean;
            }
        }
        interface TokenRequest {
            endpoint: string;
            auth_method?: string;
            method?: string;
            params?: {
                [key: string]: string;
            };
            request_content_type?: 'application/x-www-form-urlencoded' | 'application/json';
            response_content_type?: 'application/x-www-form-urlencoded' | 'application/json';
            response_map?: {
                [key: string]: string;
            };
        }
        interface UserInfoRequest {
            endpoint: string;
            triggers: UserInfoRequest.Triggers;
            auth_method?: string;
            method?: string;
            params?: {
                [key: string]: string;
            };
            request_content_type?: 'application/x-www-form-urlencoded' | 'application/json';
            response_content_type?: 'application/x-www-form-urlencoded' | 'application/json';
            response_map?: {
                [key: string]: string;
            };
        }
        namespace UserInfoRequest {
            interface Triggers {
                on_token_grant?: boolean;
                on_token_refresh?: boolean;
            }
        }
    }
}
export interface AuthProviderResponse {
    id?: string;
    binding?: AuthProviderResponse.Binding;
    created_at?: string;
    description?: string;
    oauth2?: AuthProviderResponse.Oauth2;
    provider_id?: string;
    status?: string;
    type?: string;
    updated_at?: string;
}
export declare namespace AuthProviderResponse {
    interface Binding {
        id?: string;
        type?: 'static' | 'tenant' | 'project' | 'account';
    }
    interface Oauth2 {
        authorize_request?: Oauth2.AuthorizeRequest;
        client_id?: string;
        client_secret?: Oauth2.ClientSecret;
        pkce?: Oauth2.Pkce;
        refresh_request?: Oauth2.RefreshRequest;
        scope_delimiter?: string;
        token_introspection_request?: Oauth2.TokenIntrospectionRequest;
        token_request?: Oauth2.TokenRequest;
        user_info_request?: Oauth2.UserInfoRequest;
    }
    namespace Oauth2 {
        interface AuthorizeRequest {
            auth_method?: string;
            endpoint?: string;
            expiration_format?: string;
            method?: string;
            params?: {
                [key: string]: string;
            };
            request_content_type?: string;
            response_content_type?: string;
            response_map?: {
                [key: string]: string;
            };
        }
        interface ClientSecret {
            binding?: 'static' | 'tenant' | 'project' | 'account';
            editable?: boolean;
            exists?: boolean;
            hint?: string;
            value?: string;
        }
        interface Pkce {
            code_challenge_method?: string;
            enabled?: boolean;
        }
        interface RefreshRequest {
            auth_method?: string;
            endpoint?: string;
            expiration_format?: string;
            method?: string;
            params?: {
                [key: string]: string;
            };
            request_content_type?: string;
            response_content_type?: string;
            response_map?: {
                [key: string]: string;
            };
        }
        interface TokenIntrospectionRequest {
            auth_method?: string;
            enabled?: boolean;
            endpoint?: string;
            expiration_format?: string;
            method?: string;
            params?: {
                [key: string]: string;
            };
            request_content_type?: string;
            response_content_type?: string;
            response_map?: {
                [key: string]: string;
            };
            triggers?: TokenIntrospectionRequest.Triggers;
        }
        namespace TokenIntrospectionRequest {
            interface Triggers {
                on_token_grant?: boolean;
                on_token_refresh?: boolean;
            }
        }
        interface TokenRequest {
            auth_method?: string;
            endpoint?: string;
            expiration_format?: string;
            method?: string;
            params?: {
                [key: string]: string;
            };
            request_content_type?: string;
            response_content_type?: string;
            response_map?: {
                [key: string]: string;
            };
        }
        interface UserInfoRequest {
            auth_method?: string;
            endpoint?: string;
            expiration_format?: string;
            method?: string;
            params?: {
                [key: string]: string;
            };
            request_content_type?: string;
            response_content_type?: string;
            response_map?: {
                [key: string]: string;
            };
            triggers?: UserInfoRequest.Triggers;
        }
        namespace UserInfoRequest {
            interface Triggers {
                on_token_grant?: boolean;
                on_token_refresh?: boolean;
            }
        }
    }
}
export interface AuthProviderUpdateRequest {
    id?: string;
    description?: string;
    oauth2?: AuthProviderUpdateRequest.Oauth2;
    provider_id?: string;
    status?: string;
    type?: string;
}
export declare namespace AuthProviderUpdateRequest {
    interface Oauth2 {
        authorize_request?: Oauth2.AuthorizeRequest;
        client_id?: string;
        client_secret?: string;
        pkce?: Oauth2.Pkce;
        refresh_request?: Oauth2.RefreshRequest;
        scope_delimiter?: ',' | ' ';
        token_request?: Oauth2.TokenRequest;
        user_info_request?: Oauth2.UserInfoRequest;
    }
    namespace Oauth2 {
        interface AuthorizeRequest {
            auth_method?: string;
            endpoint?: string;
            method?: string;
            params?: {
                [key: string]: string;
            };
            request_content_type?: 'application/x-www-form-urlencoded' | 'application/json';
            response_content_type?: 'application/x-www-form-urlencoded' | 'application/json';
            response_map?: {
                [key: string]: string;
            };
        }
        interface Pkce {
            code_challenge_method?: string;
            enabled?: boolean;
        }
        interface RefreshRequest {
            auth_method?: string;
            endpoint?: string;
            method?: string;
            params?: {
                [key: string]: string;
            };
            request_content_type?: 'application/x-www-form-urlencoded' | 'application/json';
            response_content_type?: 'application/x-www-form-urlencoded' | 'application/json';
            response_map?: {
                [key: string]: string;
            };
        }
        interface TokenRequest {
            auth_method?: string;
            endpoint?: string;
            method?: string;
            params?: {
                [key: string]: string;
            };
            request_content_type?: 'application/x-www-form-urlencoded' | 'application/json';
            response_content_type?: 'application/x-www-form-urlencoded' | 'application/json';
            response_map?: {
                [key: string]: string;
            };
        }
        interface UserInfoRequest {
            auth_method?: string;
            endpoint?: string;
            method?: string;
            params?: {
                [key: string]: string;
            };
            request_content_type?: 'application/x-www-form-urlencoded' | 'application/json';
            response_content_type?: 'application/x-www-form-urlencoded' | 'application/json';
            response_map?: {
                [key: string]: string;
            };
            triggers?: UserInfoRequest.Triggers;
        }
        namespace UserInfoRequest {
            interface Triggers {
                on_token_grant?: boolean;
                on_token_refresh?: boolean;
            }
        }
    }
}
export interface AuthProviderListResponse {
    items?: Array<AuthProviderResponse>;
    limit?: number;
    offset?: number;
    page_count?: number;
    total_count?: number;
}
export interface AuthProviderCreateParams {
    id: string;
    description?: string;
    oauth2?: AuthProviderCreateParams.Oauth2;
    provider_id?: string;
    status?: string;
    type?: string;
}
export declare namespace AuthProviderCreateParams {
    interface Oauth2 {
        client_id: string;
        authorize_request?: Oauth2.AuthorizeRequest;
        client_secret?: string;
        pkce?: Oauth2.Pkce;
        refresh_request?: Oauth2.RefreshRequest;
        scope_delimiter?: ',' | ' ';
        token_introspection_request?: Oauth2.TokenIntrospectionRequest;
        token_request?: Oauth2.TokenRequest;
        user_info_request?: Oauth2.UserInfoRequest;
    }
    namespace Oauth2 {
        interface AuthorizeRequest {
            endpoint: string;
            auth_method?: string;
            method?: string;
            params?: {
                [key: string]: string;
            };
            request_content_type?: 'application/x-www-form-urlencoded' | 'application/json';
            response_content_type?: 'application/x-www-form-urlencoded' | 'application/json';
            response_map?: {
                [key: string]: string;
            };
        }
        interface Pkce {
            code_challenge_method?: string;
            enabled?: boolean;
        }
        interface RefreshRequest {
            endpoint: string;
            auth_method?: string;
            method?: string;
            params?: {
                [key: string]: string;
            };
            request_content_type?: 'application/x-www-form-urlencoded' | 'application/json';
            response_content_type?: 'application/x-www-form-urlencoded' | 'application/json';
            response_map?: {
                [key: string]: string;
            };
        }
        interface TokenIntrospectionRequest {
            endpoint: string;
            triggers: TokenIntrospectionRequest.Triggers;
            auth_method?: string;
            method?: string;
            params?: {
                [key: string]: string;
            };
            request_content_type?: 'application/x-www-form-urlencoded' | 'application/json';
            response_content_type?: 'application/x-www-form-urlencoded' | 'application/json';
            response_map?: {
                [key: string]: string;
            };
        }
        namespace TokenIntrospectionRequest {
            interface Triggers {
                on_token_grant?: boolean;
                on_token_refresh?: boolean;
            }
        }
        interface TokenRequest {
            endpoint: string;
            auth_method?: string;
            method?: string;
            params?: {
                [key: string]: string;
            };
            request_content_type?: 'application/x-www-form-urlencoded' | 'application/json';
            response_content_type?: 'application/x-www-form-urlencoded' | 'application/json';
            response_map?: {
                [key: string]: string;
            };
        }
        interface UserInfoRequest {
            endpoint: string;
            triggers: UserInfoRequest.Triggers;
            auth_method?: string;
            method?: string;
            params?: {
                [key: string]: string;
            };
            request_content_type?: 'application/x-www-form-urlencoded' | 'application/json';
            response_content_type?: 'application/x-www-form-urlencoded' | 'application/json';
            response_map?: {
                [key: string]: string;
            };
        }
        namespace UserInfoRequest {
            interface Triggers {
                on_token_grant?: boolean;
                on_token_refresh?: boolean;
            }
        }
    }
}
export interface AuthProviderPatchParams {
    body_id?: string;
    description?: string;
    oauth2?: AuthProviderPatchParams.Oauth2;
    provider_id?: string;
    status?: string;
    type?: string;
}
export declare namespace AuthProviderPatchParams {
    interface Oauth2 {
        authorize_request?: Oauth2.AuthorizeRequest;
        client_id?: string;
        client_secret?: string;
        pkce?: Oauth2.Pkce;
        refresh_request?: Oauth2.RefreshRequest;
        scope_delimiter?: ',' | ' ';
        token_request?: Oauth2.TokenRequest;
        user_info_request?: Oauth2.UserInfoRequest;
    }
    namespace Oauth2 {
        interface AuthorizeRequest {
            auth_method?: string;
            endpoint?: string;
            method?: string;
            params?: {
                [key: string]: string;
            };
            request_content_type?: 'application/x-www-form-urlencoded' | 'application/json';
            response_content_type?: 'application/x-www-form-urlencoded' | 'application/json';
            response_map?: {
                [key: string]: string;
            };
        }
        interface Pkce {
            code_challenge_method?: string;
            enabled?: boolean;
        }
        interface RefreshRequest {
            auth_method?: string;
            endpoint?: string;
            method?: string;
            params?: {
                [key: string]: string;
            };
            request_content_type?: 'application/x-www-form-urlencoded' | 'application/json';
            response_content_type?: 'application/x-www-form-urlencoded' | 'application/json';
            response_map?: {
                [key: string]: string;
            };
        }
        interface TokenRequest {
            auth_method?: string;
            endpoint?: string;
            method?: string;
            params?: {
                [key: string]: string;
            };
            request_content_type?: 'application/x-www-form-urlencoded' | 'application/json';
            response_content_type?: 'application/x-www-form-urlencoded' | 'application/json';
            response_map?: {
                [key: string]: string;
            };
        }
        interface UserInfoRequest {
            auth_method?: string;
            endpoint?: string;
            method?: string;
            params?: {
                [key: string]: string;
            };
            request_content_type?: 'application/x-www-form-urlencoded' | 'application/json';
            response_content_type?: 'application/x-www-form-urlencoded' | 'application/json';
            response_map?: {
                [key: string]: string;
            };
            triggers?: UserInfoRequest.Triggers;
        }
        namespace UserInfoRequest {
            interface Triggers {
                on_token_grant?: boolean;
                on_token_refresh?: boolean;
            }
        }
    }
}
export declare namespace AuthProviders {
    export { type AuthProviderCreateRequest as AuthProviderCreateRequest, type AuthProviderResponse as AuthProviderResponse, type AuthProviderUpdateRequest as AuthProviderUpdateRequest, type AuthProviderListResponse as AuthProviderListResponse, type AuthProviderCreateParams as AuthProviderCreateParams, type AuthProviderPatchParams as AuthProviderPatchParams, };
}
//# sourceMappingURL=auth-providers.d.ts.map