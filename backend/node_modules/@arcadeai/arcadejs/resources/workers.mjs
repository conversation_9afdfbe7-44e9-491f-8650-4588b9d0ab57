// File generated from our OpenAPI spec by Stainless. See CONTRIBUTING.md for details.
import { APIResource } from "../resource.mjs";
import { isRequestOptions } from "../core.mjs";
import { ToolDefinitionsOffsetPage } from "./tools/tools.mjs";
import { OffsetPage } from "../pagination.mjs";
export class Workers extends APIResource {
    /**
     * Create a worker
     */
    create(body, options) {
        return this._client.post('/v1/workers', { body, ...options });
    }
    /**
     * Update a worker
     */
    update(id, body, options) {
        return this._client.patch(`/v1/workers/${id}`, { body, ...options });
    }
    list(query = {}, options) {
        if (isRequestOptions(query)) {
            return this.list({}, query);
        }
        return this._client.getAPIList('/v1/workers', WorkerResponsesOffsetPage, { query, ...options });
    }
    /**
     * Delete a worker
     */
    delete(id, options) {
        return this._client.delete(`/v1/workers/${id}`, {
            ...options,
            headers: { Accept: '*/*', ...options?.headers },
        });
    }
    /**
     * Get a worker by ID
     */
    get(id, options) {
        return this._client.get(`/v1/workers/${id}`, options);
    }
    /**
     * Get the health of a worker
     */
    health(id, options) {
        return this._client.get(`/v1/workers/${id}/health`, options);
    }
    tools(id, query = {}, options) {
        if (isRequestOptions(query)) {
            return this.tools(id, {}, query);
        }
        return this._client.getAPIList(`/v1/workers/${id}/tools`, ToolDefinitionsOffsetPage, {
            query,
            ...options,
        });
    }
}
export class WorkerResponsesOffsetPage extends OffsetPage {
}
Workers.WorkerResponsesOffsetPage = WorkerResponsesOffsetPage;
export { ToolDefinitionsOffsetPage };
//# sourceMappingURL=workers.mjs.map