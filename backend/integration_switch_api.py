"""
API pour la gestion de la migration Pipedream → Arcade.dev
Permet de basculer entre les deux intégrations de manière progressive
"""

from fastapi import APIRouter, HTTPException, Depends
from typing import Dict, Any, Optional
from pydantic import BaseModel

from utils.logger import logger
from utils.auth_utils import get_current_user_id_from_jwt
from integration_adapter import integration_adapter

router = APIRouter(prefix="/integration", tags=["integration"])


class IntegrationStatusResponse(BaseModel):
    current_provider: str
    arcade_enabled: bool
    migration_mode: str
    user_preference: Optional[str] = None


class SwitchProviderRequest(BaseModel):
    provider: str  # "pipedream" ou "arcade"
    user_id: Optional[str] = None


class MigrationStatsResponse(BaseModel):
    total_users: int
    arcade_users: int
    pipedream_users: int
    migration_percentage: float


@router.get("/status", response_model=IntegrationStatusResponse)
async def get_integration_status(user_id: str = Depends(get_current_user_id_from_jwt)):
    """Récupère le statut de l'intégration pour l'utilisateur actuel"""
    try:
        current_provider = "arcade" if integration_adapter.should_use_arcade(user_id) else "pipedream"
        
        return IntegrationStatusResponse(
            current_provider=current_provider,
            arcade_enabled=integration_adapter.arcade_enabled,
            migration_mode=integration_adapter.migration_mode,
            user_preference=integration_adapter._get_user_integration_preference(user_id) if integration_adapter.migration_mode == "user_choice" else None
        )
        
    except Exception as e:
        logger.error(f"Error getting integration status: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/switch")
async def switch_integration_provider(
    request: SwitchProviderRequest,
    admin_user_id: str = Depends(get_current_user_id_from_jwt)
):
    """Bascule le provider d'intégration (admin uniquement)"""
    try:
        # TODO: Vérifier que l'utilisateur est admin
        # if not is_admin(admin_user_id):
        #     raise HTTPException(status_code=403, detail="Admin access required")
        
        if request.provider not in ["pipedream", "arcade"]:
            raise HTTPException(status_code=400, detail="Provider must be 'pipedream' or 'arcade'")
        
        if integration_adapter.migration_mode == "feature_flag":
            # Mode feature flag global
            import os
            os.environ["ENABLE_ARCADE_INTEGRATION"] = "true" if request.provider == "arcade" else "false"
            integration_adapter.arcade_enabled = request.provider == "arcade"
            
            return {
                "success": True,
                "message": f"Global integration switched to {request.provider}",
                "provider": request.provider
            }
            
        elif integration_adapter.migration_mode == "user_choice" and request.user_id:
            # Mode choix utilisateur
            await _set_user_integration_preference(request.user_id, request.provider)
            
            return {
                "success": True,
                "message": f"User {request.user_id} integration switched to {request.provider}",
                "provider": request.provider,
                "user_id": request.user_id
            }
        else:
            raise HTTPException(status_code=400, detail="Invalid migration mode or missing user_id")
            
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error switching integration provider: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/migration-stats", response_model=MigrationStatsResponse)
async def get_migration_stats(admin_user_id: str = Depends(get_current_user_id_from_jwt)):
    """Récupère les statistiques de migration (admin uniquement)"""
    try:
        # TODO: Vérifier que l'utilisateur est admin
        # if not is_admin(admin_user_id):
        #     raise HTTPException(status_code=403, detail="Admin access required")
        
        if integration_adapter.migration_mode == "feature_flag":
            # En mode feature flag, tous les utilisateurs utilisent le même provider
            total_users = await _get_total_users_count()
            if integration_adapter.arcade_enabled:
                arcade_users = total_users
                pipedream_users = 0
            else:
                arcade_users = 0
                pipedream_users = total_users
        else:
            # En mode user_choice, compter les préférences individuelles
            total_users = await _get_total_users_count()
            arcade_users = await _get_arcade_users_count()
            pipedream_users = total_users - arcade_users
        
        migration_percentage = (arcade_users / total_users * 100) if total_users > 0 else 0
        
        return MigrationStatsResponse(
            total_users=total_users,
            arcade_users=arcade_users,
            pipedream_users=pipedream_users,
            migration_percentage=migration_percentage
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting migration stats: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/test-connection")
async def test_integration_connection(
    provider: str,
    user_id: str = Depends(get_current_user_id_from_jwt)
):
    """Teste la connexion avec un provider spécifique"""
    try:
        if provider not in ["pipedream", "arcade"]:
            raise HTTPException(status_code=400, detail="Provider must be 'pipedream' or 'arcade'")
        
        if provider == "arcade":
            # Tester la connexion Arcade
            from arcade.support.arcade_client import ArcadeClient
            
            arcade_client = ArcadeClient()
            toolkits_response = await arcade_client.list_toolkits(limit=5)
            
            return {
                "success": True,
                "provider": "arcade",
                "message": "Arcade connection successful",
                "test_data": {
                    "toolkits_found": len(toolkits_response.get("toolkits", [])),
                    "sample_toolkits": [t["name"] for t in toolkits_response.get("toolkits", [])[:3]]
                }
            }
        else:
            # Tester la connexion Pipedream
            from pipedream.facade import PipedreamManager
            
            pipedream_manager = PipedreamManager()
            # TODO: Implémenter un test de connexion Pipedream
            
            return {
                "success": True,
                "provider": "pipedream",
                "message": "Pipedream connection successful",
                "test_data": {
                    "status": "connected"
                }
            }
            
    except Exception as e:
        logger.error(f"Error testing {provider} connection: {str(e)}")
        return {
            "success": False,
            "provider": provider,
            "message": f"Connection test failed: {str(e)}"
        }


@router.get("/health")
async def integration_health_check():
    """Health check pour les intégrations"""
    try:
        status = {
            "integration_adapter": "healthy",
            "arcade_enabled": integration_adapter.arcade_enabled,
            "migration_mode": integration_adapter.migration_mode,
            "providers": {}
        }
        
        # Test Arcade si activé
        if integration_adapter.arcade_enabled:
            try:
                from arcade.support.arcade_client import ArcadeClient
                arcade_client = ArcadeClient()
                await arcade_client.list_toolkits(limit=1)
                status["providers"]["arcade"] = "healthy"
            except Exception as e:
                status["providers"]["arcade"] = f"error: {str(e)}"
        
        # Test Pipedream
        try:
            from pipedream.facade import PipedreamManager
            pipedream_manager = PipedreamManager()
            # TODO: Ajouter un test de santé Pipedream
            status["providers"]["pipedream"] = "healthy"
        except Exception as e:
            status["providers"]["pipedream"] = f"error: {str(e)}"
        
        return status
        
    except Exception as e:
        logger.error(f"Integration health check failed: {str(e)}")
        return {
            "integration_adapter": "error",
            "error": str(e)
        }


# Fonctions utilitaires

async def _set_user_integration_preference(user_id: str, provider: str):
    """Définit la préférence d'intégration d'un utilisateur"""
    try:
        from services.supabase import DBConnection
        
        db = DBConnection()
        client = await db.client
        
        # Créer ou mettre à jour la préférence utilisateur
        await client.table('user_integration_preferences').upsert({
            'user_id': user_id,
            'provider': provider,
            'updated_at': 'now()'
        }).execute()
        
        logger.info(f"Set integration preference for user {user_id}: {provider}")
        
    except Exception as e:
        logger.error(f"Error setting user integration preference: {str(e)}")
        raise


async def _get_total_users_count() -> int:
    """Récupère le nombre total d'utilisateurs"""
    try:
        from services.supabase import DBConnection
        
        db = DBConnection()
        client = await db.client
        
        result = await client.table('agents').select('account_id', count='exact').execute()
        return result.count or 0
        
    except Exception as e:
        logger.error(f"Error getting total users count: {str(e)}")
        return 0


async def _get_arcade_users_count() -> int:
    """Récupère le nombre d'utilisateurs utilisant Arcade"""
    try:
        from services.supabase import DBConnection
        
        db = DBConnection()
        client = await db.client
        
        result = await client.table('user_integration_preferences').select(
            'user_id', count='exact'
        ).eq('provider', 'arcade').execute()
        
        return result.count or 0
        
    except Exception as e:
        logger.error(f"Error getting Arcade users count: {str(e)}")
        return 0
