import asyncio
import os
from typing import Dict, List, Any, Optional
from utils.logger import logger
from mcp import ClientSession, StdioServerParameters
from mcp.client.stdio import stdio_client


class ArcadeMCPHandler:
    """
    Gestionnaire MCP pour Arcade.dev
    Remplace la logique Pipedream dans custom_mcp_handler.py
    """
    
    def __init__(self):
        self.active_sessions: Dict[str, ClientSession] = {}
        self.arcade_api_key = os.getenv("ARCADE_API_KEY")
        
    async def initialize_arcade_mcp(
        self, 
        server_name: str, 
        server_config: Dict[str, Any], 
        enabled_tools: List[str]
    ):
        """
        Initialise une connexion MCP Arcade
        Compatible avec la structure Pipedream existante
        """
        try:
            # Configuration Arcade MCP
            toolkit_name = server_config.get('toolkit_name', 'gmail')  # Default toolkit
            user_id = server_config.get('user_id')
            profile_id = server_config.get('profile_id')
            
            if not user_id:
                logger.error(f"Arcade MCP {server_name}: Missing user_id in config")
                return
            
            # URL du serveur MCP Arcade (peut être local ou hébergé)
            mcp_server_url = server_config.get('url', 'https://api.arcade.dev/mcp')
            
            # Headers pour l'authentification Arcade
            headers = {
                'Authorization': f'Bearer {self.arcade_api_key}',
                'X-Arcade-User-ID': user_id,
                'X-Arcade-Toolkit': toolkit_name
            }
            
            if profile_id:
                headers['X-Arcade-Profile-ID'] = profile_id
            
            # Ajouter les headers personnalisés du config
            if 'headers' in server_config:
                headers.update(server_config['headers'])
            
            logger.info(f"Arcade MCP {server_name}: Connecting to {mcp_server_url} for toolkit {toolkit_name}")
            
            # Créer les paramètres du serveur MCP
            if mcp_server_url.startswith('stdio://'):
                # Connexion stdio pour serveur local
                command = server_config.get('command', ['arcade', 'serve', '--mcp'])
                env = os.environ.copy()
                env.update(server_config.get('env', {}))
                
                server_params = StdioServerParameters(
                    command=command,
                    env=env
                )
                
                # Connexion stdio
                async with stdio_client(server_params) as (read, write):
                    async with ClientSession(read, write) as session:
                        await session.initialize()
                        
                        # Lister les outils disponibles
                        tools_result = await session.list_tools()
                        available_tools = tools_result.tools if hasattr(tools_result, 'tools') else tools_result
                        
                        # Filtrer les outils activés
                        filtered_tools = []
                        for tool in available_tools:
                            if not enabled_tools or tool.name in enabled_tools:
                                filtered_tools.append(tool)
                        
                        logger.info(f"Arcade MCP {server_name}: Connected with {len(filtered_tools)} enabled tools")
                        
                        # Stocker la session
                        self.active_sessions[server_name] = session
                        
            else:
                # Connexion HTTP/WebSocket pour serveur distant
                from mcp.client.sse import sse_client
                from mcp.client.session import ClientSession
                
                # Configuration pour connexion SSE/HTTP
                async with sse_client(mcp_server_url, headers=headers) as (read, write):
                    async with ClientSession(read, write) as session:
                        await session.initialize()
                        
                        # Lister les outils disponibles
                        tools_result = await session.list_tools()
                        available_tools = tools_result.tools if hasattr(tools_result, 'tools') else tools_result
                        
                        # Filtrer les outils activés
                        filtered_tools = []
                        for tool in available_tools:
                            if not enabled_tools or tool.name in enabled_tools:
                                filtered_tools.append(tool)
                        
                        logger.info(f"Arcade MCP {server_name}: Connected with {len(filtered_tools)} enabled tools")
                        
                        # Stocker la session
                        self.active_sessions[server_name] = session
            
        except Exception as e:
            logger.error(f"Arcade MCP {server_name}: Failed to initialize - {str(e)}")
            raise
    
    async def execute_arcade_tool(
        self, 
        server_name: str, 
        tool_name: str, 
        arguments: Dict[str, Any]
    ) -> Any:
        """
        Exécute un outil Arcade via MCP
        """
        if server_name not in self.active_sessions:
            raise ValueError(f"Arcade MCP server {server_name} not initialized")
        
        session = self.active_sessions[server_name]
        
        try:
            logger.info(f"Arcade MCP {server_name}: Executing tool {tool_name}")
            
            # Exécuter l'outil via MCP
            result = await session.call_tool(tool_name, arguments)
            
            logger.info(f"Arcade MCP {server_name}: Tool {tool_name} executed successfully")
            return result
            
        except Exception as e:
            logger.error(f"Arcade MCP {server_name}: Tool execution failed - {str(e)}")
            raise
    
    async def list_arcade_tools(self, server_name: str) -> List[Dict[str, Any]]:
        """
        Liste les outils disponibles d'un serveur Arcade MCP
        """
        if server_name not in self.active_sessions:
            raise ValueError(f"Arcade MCP server {server_name} not initialized")
        
        session = self.active_sessions[server_name]
        
        try:
            tools_result = await session.list_tools()
            tools = tools_result.tools if hasattr(tools_result, 'tools') else tools_result
            
            tool_list = []
            for tool in tools:
                tool_info = {
                    'name': tool.name,
                    'description': tool.description,
                    'inputSchema': tool.inputSchema if hasattr(tool, 'inputSchema') else {}
                }
                tool_list.append(tool_info)
            
            return tool_list
            
        except Exception as e:
            logger.error(f"Arcade MCP {server_name}: Failed to list tools - {str(e)}")
            raise
    
    async def close_session(self, server_name: str):
        """
        Ferme une session MCP Arcade
        """
        if server_name in self.active_sessions:
            try:
                session = self.active_sessions[server_name]
                # La session se ferme automatiquement avec le context manager
                del self.active_sessions[server_name]
                logger.info(f"Arcade MCP {server_name}: Session closed")
            except Exception as e:
                logger.error(f"Arcade MCP {server_name}: Error closing session - {str(e)}")
    
    async def close_all_sessions(self):
        """
        Ferme toutes les sessions MCP Arcade
        """
        for server_name in list(self.active_sessions.keys()):
            await self.close_session(server_name)
    
    def get_arcade_mcp_config(
        self, 
        toolkit_name: str, 
        user_id: str, 
        profile_id: str,
        enabled_tools: List[str]
    ) -> Dict[str, Any]:
        """
        Génère une configuration MCP Arcade compatible avec l'existant
        """
        return {
            "name": f"Arcade {toolkit_name.title()}",
            "type": "arcade",
            "config": {
                "url": "https://api.arcade.dev/mcp",  # ou stdio://arcade-mcp pour local
                "toolkit_name": toolkit_name,
                "user_id": user_id,
                "profile_id": profile_id,
                "headers": {
                    "X-Arcade-Toolkit": toolkit_name,
                    "X-Arcade-User-ID": user_id,
                    "X-Arcade-Profile-ID": profile_id
                }
            },
            "enabledTools": enabled_tools,
            "enabled_tools": enabled_tools  # Compatibilité double naming
        }
    
    def is_arcade_mcp(self, mcp_config: Dict[str, Any]) -> bool:
        """
        Vérifie si une configuration MCP est pour Arcade
        """
        mcp_type = mcp_config.get('type', '').lower()
        return mcp_type == 'arcade' or 'arcade' in mcp_config.get('name', '').lower()


# Instance globale du gestionnaire Arcade MCP
arcade_mcp_handler = ArcadeMCPHandler()
