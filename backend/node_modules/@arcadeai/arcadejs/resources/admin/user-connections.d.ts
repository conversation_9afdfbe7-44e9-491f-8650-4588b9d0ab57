import { APIResource } from "../../resource.js";
import * as Core from "../../core.js";
import { OffsetPage, type OffsetPageParams } from "../../pagination.js";
export declare class UserConnections extends APIResource {
    /**
     * List all auth connections
     */
    list(query?: UserConnectionListParams, options?: Core.RequestOptions): Core.PagePromise<UserConnectionResponsesOffsetPage, UserConnectionResponse>;
    list(options?: Core.RequestOptions): Core.PagePromise<UserConnectionResponsesOffsetPage, UserConnectionResponse>;
    /**
     * Delete a user/auth provider connection
     */
    delete(id: string, options?: Core.RequestOptions): Core.APIPromise<void>;
}
export declare class UserConnectionResponsesOffsetPage extends OffsetPage<UserConnectionResponse> {
}
export interface UserConnectionResponse {
    id?: string;
    connection_id?: string;
    connection_status?: string;
    provider_description?: string;
    provider_id?: string;
    provider_user_info?: unknown;
    scopes?: Array<string>;
    user_id?: string;
}
export interface UserConnectionListParams extends OffsetPageParams {
    provider?: UserConnectionListParams.Provider;
    user?: UserConnectionListParams.User;
}
export declare namespace UserConnectionListParams {
    interface Provider {
        /**
         * Provider ID
         */
        id?: string;
    }
    interface User {
        /**
         * User ID
         */
        id?: string;
    }
}
export declare namespace UserConnections {
    export { type UserConnectionResponse as UserConnectionResponse, UserConnectionResponsesOffsetPage as UserConnectionResponsesOffsetPage, type UserConnectionListParams as UserConnectionListParams, };
}
//# sourceMappingURL=user-connections.d.ts.map