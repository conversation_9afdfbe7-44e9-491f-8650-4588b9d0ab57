import { APIResource } from "../resource.js";
import * as Core from "../core.js";
export declare class Health extends APIResource {
    /**
     * Engine Health
     */
    check(options?: Core.RequestOptions): Core.APIPromise<HealthSchema>;
}
export interface HealthSchema {
    healthy?: boolean;
}
export declare namespace Health {
    export { type HealthSchema as HealthSchema };
}
//# sourceMappingURL=health.d.ts.map