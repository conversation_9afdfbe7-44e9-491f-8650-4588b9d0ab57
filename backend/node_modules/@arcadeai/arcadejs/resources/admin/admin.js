"use strict";
// File generated from our OpenAPI spec by Stainless. See CONTRIBUTING.md for details.
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.Admin = void 0;
const resource_1 = require("../../resource.js");
const AuthProvidersAPI = __importStar(require("./auth-providers.js"));
const auth_providers_1 = require("./auth-providers.js");
const SecretsAPI = __importStar(require("./secrets.js"));
const secrets_1 = require("./secrets.js");
const UserConnectionsAPI = __importStar(require("./user-connections.js"));
const user_connections_1 = require("./user-connections.js");
class Admin extends resource_1.APIResource {
    constructor() {
        super(...arguments);
        this.userConnections = new UserConnectionsAPI.UserConnections(this._client);
        this.authProviders = new AuthProvidersAPI.AuthProviders(this._client);
        this.secrets = new SecretsAPI.Secrets(this._client);
    }
}
exports.Admin = Admin;
Admin.UserConnections = user_connections_1.UserConnections;
Admin.UserConnectionResponsesOffsetPage = user_connections_1.UserConnectionResponsesOffsetPage;
Admin.AuthProviders = auth_providers_1.AuthProviders;
Admin.Secrets = secrets_1.Secrets;
//# sourceMappingURL=admin.js.map