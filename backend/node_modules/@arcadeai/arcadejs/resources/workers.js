"use strict";
// File generated from our OpenAPI spec by Stainless. See CONTRIBUTING.md for details.
Object.defineProperty(exports, "__esModule", { value: true });
exports.ToolDefinitionsOffsetPage = exports.WorkerResponsesOffsetPage = exports.Workers = void 0;
const resource_1 = require("../resource.js");
const core_1 = require("../core.js");
const tools_1 = require("./tools/tools.js");
Object.defineProperty(exports, "ToolDefinitionsOffsetPage", { enumerable: true, get: function () { return tools_1.ToolDefinitionsOffsetPage; } });
const pagination_1 = require("../pagination.js");
class Workers extends resource_1.APIResource {
    /**
     * Create a worker
     */
    create(body, options) {
        return this._client.post('/v1/workers', { body, ...options });
    }
    /**
     * Update a worker
     */
    update(id, body, options) {
        return this._client.patch(`/v1/workers/${id}`, { body, ...options });
    }
    list(query = {}, options) {
        if ((0, core_1.isRequestOptions)(query)) {
            return this.list({}, query);
        }
        return this._client.getAPIList('/v1/workers', WorkerResponsesOffsetPage, { query, ...options });
    }
    /**
     * Delete a worker
     */
    delete(id, options) {
        return this._client.delete(`/v1/workers/${id}`, {
            ...options,
            headers: { Accept: '*/*', ...options?.headers },
        });
    }
    /**
     * Get a worker by ID
     */
    get(id, options) {
        return this._client.get(`/v1/workers/${id}`, options);
    }
    /**
     * Get the health of a worker
     */
    health(id, options) {
        return this._client.get(`/v1/workers/${id}/health`, options);
    }
    tools(id, query = {}, options) {
        if ((0, core_1.isRequestOptions)(query)) {
            return this.tools(id, {}, query);
        }
        return this._client.getAPIList(`/v1/workers/${id}/tools`, tools_1.ToolDefinitionsOffsetPage, {
            query,
            ...options,
        });
    }
}
exports.Workers = Workers;
class WorkerResponsesOffsetPage extends pagination_1.OffsetPage {
}
exports.WorkerResponsesOffsetPage = WorkerResponsesOffsetPage;
Workers.WorkerResponsesOffsetPage = WorkerResponsesOffsetPage;
//# sourceMappingURL=workers.js.map