#!/usr/bin/env python3
"""
Script de migration des données Pipedream vers Arcade.dev
Migre les profils, connexions et configurations MCP existants
"""

import asyncio
import json
import os
import sys
from typing import List, Dict, Any, Optional
from datetime import datetime

# Ajouter le répertoire parent au path pour les imports
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from services.supabase import DBConnection
from utils.logger import logger
from utils.encryption import decrypt_data, encrypt_data


class PipedreamToArcadeMigrator:
    """
    Migrateur pour convertir les données Pipedream vers Arcade
    """
    
    def __init__(self):
        self.db = DBConnection()
        self.migration_stats = {
            "profiles_migrated": 0,
            "profiles_failed": 0,
            "mcps_updated": 0,
            "mcps_failed": 0,
            "agents_updated": 0,
            "agents_failed": 0
        }
    
    async def run_migration(self, dry_run: bool = True):
        """
        Lance la migration complète
        """
        logger.info(f"Starting Pipedream to Arcade migration (dry_run={dry_run})")
        
        try:
            client = await self.db.client
            
            # 1. Migrer les profils Pipedream vers Arcade
            await self._migrate_profiles(client, dry_run)
            
            # 2. Mettre à jour les configurations MCP dans les agents
            await self._update_agent_mcps(client, dry_run)
            
            # 3. Mettre à jour les versions d'agents
            await self._update_agent_versions(client, dry_run)
            
            # Afficher les statistiques
            self._print_migration_stats(dry_run)
            
            if dry_run:
                logger.info("Migration completed in DRY RUN mode - no changes were made")
            else:
                logger.info("Migration completed successfully!")
                
        except Exception as e:
            logger.error(f"Migration failed: {str(e)}")
            raise
    
    async def _migrate_profiles(self, client, dry_run: bool):
        """
        Migre les profils Pipedream vers des profils Arcade
        """
        logger.info("Migrating Pipedream profiles to Arcade profiles...")
        
        try:
            # Récupérer tous les profils Pipedream
            result = await client.table('user_mcp_credential_profiles').select('*').execute()
            
            pipedream_profiles = result.data or []
            logger.info(f"Found {len(pipedream_profiles)} Pipedream profiles to migrate")
            
            for profile in pipedream_profiles:
                try:
                    await self._migrate_single_profile(client, profile, dry_run)
                    self.migration_stats["profiles_migrated"] += 1
                except Exception as e:
                    logger.error(f"Failed to migrate profile {profile.get('profile_id')}: {str(e)}")
                    self.migration_stats["profiles_failed"] += 1
                    
        except Exception as e:
            logger.error(f"Error migrating profiles: {str(e)}")
            raise
    
    async def _migrate_single_profile(self, client, pipedream_profile: Dict[str, Any], dry_run: bool):
        """
        Migre un profil Pipedream individuel vers Arcade
        """
        profile_id = pipedream_profile['profile_id']
        account_id = pipedream_profile['account_id']
        profile_name = pipedream_profile['profile_name']
        
        # Décrypter la configuration
        encrypted_config = pipedream_profile['encrypted_config']
        decrypted_config = decrypt_data(encrypted_config)
        config_data = json.loads(decrypted_config)
        
        # Extraire les informations Pipedream
        app_slug = config_data.get('app_slug', 'unknown')
        external_user_id = config_data.get('external_user_id')
        oauth_app_id = config_data.get('oauth_app_id')
        
        # Mapper app_slug Pipedream vers toolkit Arcade
        toolkit_name = self._map_pipedream_to_arcade_toolkit(app_slug)
        app_name = self._get_app_display_name(toolkit_name)
        
        logger.info(f"Migrating profile {profile_name}: {app_slug} -> {toolkit_name}")
        
        if not dry_run:
            # Créer le profil Arcade
            arcade_profile_data = {
                'profile_id': profile_id,  # Garder le même ID
                'account_id': account_id,
                'profile_name': profile_name,
                'toolkit_name': toolkit_name,
                'app_name': app_name,
                'description': f'Migrated from Pipedream ({app_slug})',
                'is_active': True,
                'is_default': False,
                'enabled_tools': [],  # À configurer manuellement après migration
                'external_user_id': external_user_id,
                'oauth_app_id': oauth_app_id,
                'created_at': pipedream_profile.get('created_at', datetime.utcnow().isoformat()),
                'updated_at': datetime.utcnow().isoformat()
            }
            
            # Insérer dans la table arcade_profiles
            await client.table('arcade_profiles').upsert(arcade_profile_data).execute()
            
            logger.info(f"Created Arcade profile: {profile_id}")
    
    async def _update_agent_mcps(self, client, dry_run: bool):
        """
        Met à jour les configurations MCP dans les agents
        """
        logger.info("Updating agent MCP configurations...")
        
        try:
            # Récupérer tous les agents avec des MCPs personnalisés
            result = await client.table('agents').select('*').neq('custom_mcps', '[]').execute()
            
            agents = result.data or []
            logger.info(f"Found {len(agents)} agents with custom MCPs")
            
            for agent in agents:
                try:
                    await self._update_single_agent_mcps(client, agent, dry_run)
                    self.migration_stats["agents_updated"] += 1
                except Exception as e:
                    logger.error(f"Failed to update agent {agent.get('agent_id')}: {str(e)}")
                    self.migration_stats["agents_failed"] += 1
                    
        except Exception as e:
            logger.error(f"Error updating agent MCPs: {str(e)}")
            raise
    
    async def _update_single_agent_mcps(self, client, agent: Dict[str, Any], dry_run: bool):
        """
        Met à jour les MCPs d'un agent individuel
        """
        agent_id = agent['agent_id']
        custom_mcps = agent.get('custom_mcps', [])
        
        if not custom_mcps:
            return
        
        updated_mcps = []
        mcps_changed = False
        
        for mcp in custom_mcps:
            if mcp.get('type') == 'pipedream' or mcp.get('customType') == 'pipedream':
                # Convertir MCP Pipedream vers Arcade
                updated_mcp = self._convert_pipedream_mcp_to_arcade(mcp)
                updated_mcps.append(updated_mcp)
                mcps_changed = True
                self.migration_stats["mcps_updated"] += 1
                
                logger.info(f"Converted MCP in agent {agent_id}: {mcp.get('name')} -> Arcade")
            else:
                # Garder les autres MCPs inchangés
                updated_mcps.append(mcp)
        
        if mcps_changed and not dry_run:
            # Mettre à jour l'agent
            await client.table('agents').update({
                'custom_mcps': updated_mcps,
                'updated_at': datetime.utcnow().isoformat()
            }).eq('agent_id', agent_id).execute()
            
            logger.info(f"Updated agent {agent_id} with converted MCPs")
    
    async def _update_agent_versions(self, client, dry_run: bool):
        """
        Met à jour les versions d'agents avec les nouveaux MCPs
        """
        logger.info("Updating agent versions...")
        
        try:
            # Récupérer toutes les versions avec des MCPs personnalisés
            result = await client.table('agent_versions').select('*').neq('custom_mcps', '[]').execute()
            
            versions = result.data or []
            logger.info(f"Found {len(versions)} agent versions with custom MCPs")
            
            for version in versions:
                try:
                    await self._update_single_version_mcps(client, version, dry_run)
                except Exception as e:
                    logger.error(f"Failed to update version {version.get('version_id')}: {str(e)}")
                    
        except Exception as e:
            logger.error(f"Error updating agent versions: {str(e)}")
            raise
    
    async def _update_single_version_mcps(self, client, version: Dict[str, Any], dry_run: bool):
        """
        Met à jour les MCPs d'une version d'agent
        """
        version_id = version['version_id']
        custom_mcps = version.get('custom_mcps', [])
        
        if not custom_mcps:
            return
        
        updated_mcps = []
        mcps_changed = False
        
        for mcp in custom_mcps:
            if mcp.get('type') == 'pipedream' or mcp.get('customType') == 'pipedream':
                # Convertir MCP Pipedream vers Arcade
                updated_mcp = self._convert_pipedream_mcp_to_arcade(mcp)
                updated_mcps.append(updated_mcp)
                mcps_changed = True
            else:
                # Garder les autres MCPs inchangés
                updated_mcps.append(mcp)
        
        if mcps_changed and not dry_run:
            # Mettre à jour la version
            await client.table('agent_versions').update({
                'custom_mcps': updated_mcps,
                'updated_at': datetime.utcnow().isoformat()
            }).eq('version_id', version_id).execute()
            
            logger.info(f"Updated version {version_id} with converted MCPs")
    
    def _convert_pipedream_mcp_to_arcade(self, pipedream_mcp: Dict[str, Any]) -> Dict[str, Any]:
        """
        Convertit une configuration MCP Pipedream vers Arcade
        """
        config = pipedream_mcp.get('config', {})
        profile_id = config.get('profile_id')
        
        # Récupérer les informations du profil pour la conversion
        app_slug = None
        if 'headers' in config and 'x-pd-app-slug' in config['headers']:
            app_slug = config['headers']['x-pd-app-slug']
        
        toolkit_name = self._map_pipedream_to_arcade_toolkit(app_slug) if app_slug else 'unknown'
        
        # Créer la nouvelle configuration Arcade
        arcade_mcp = {
            "name": pipedream_mcp.get('name', 'Arcade Integration'),
            "type": "arcade",
            "config": {
                "url": "https://api.arcade.dev/mcp",
                "toolkit_name": toolkit_name,
                "profile_id": profile_id,
                "headers": {
                    "X-Arcade-Toolkit": toolkit_name,
                    "X-Arcade-Profile-ID": profile_id
                }
            },
            "enabledTools": pipedream_mcp.get('enabledTools', pipedream_mcp.get('enabled_tools', [])),
            "enabled_tools": pipedream_mcp.get('enabledTools', pipedream_mcp.get('enabled_tools', []))
        }
        
        return arcade_mcp
    
    def _map_pipedream_to_arcade_toolkit(self, app_slug: str) -> str:
        """
        Mappe un app_slug Pipedream vers un nom de toolkit Arcade
        """
        mapping = {
            'gmail': 'gmail',
            'google-gmail': 'gmail',
            'slack': 'slack',
            'github': 'github',
            'notion': 'notion',
            'google-drive': 'google-drive',
            'google-calendar': 'calendar',
            'hubspot': 'hubspot',
            'salesforce': 'salesforce',
            'jira': 'jira',
            'asana': 'asana',
            'trello': 'trello',
            'discord': 'discord',
            'twitter': 'twitter',
            'linkedin': 'linkedin'
        }
        
        return mapping.get(app_slug, app_slug or 'unknown')
    
    def _get_app_display_name(self, toolkit_name: str) -> str:
        """
        Récupère le nom d'affichage d'un toolkit
        """
        display_names = {
            'gmail': 'Gmail',
            'slack': 'Slack',
            'github': 'GitHub',
            'notion': 'Notion',
            'google-drive': 'Google Drive',
            'calendar': 'Google Calendar',
            'hubspot': 'HubSpot',
            'salesforce': 'Salesforce',
            'jira': 'Jira',
            'asana': 'Asana',
            'trello': 'Trello',
            'discord': 'Discord',
            'twitter': 'Twitter',
            'linkedin': 'LinkedIn'
        }
        
        return display_names.get(toolkit_name, toolkit_name.title())
    
    def _print_migration_stats(self, dry_run: bool):
        """
        Affiche les statistiques de migration
        """
        print("\n" + "="*60)
        print(f"MIGRATION STATISTICS {'(DRY RUN)' if dry_run else ''}")
        print("="*60)
        print(f"Profiles migrated: {self.migration_stats['profiles_migrated']}")
        print(f"Profiles failed: {self.migration_stats['profiles_failed']}")
        print(f"MCPs updated: {self.migration_stats['mcps_updated']}")
        print(f"MCPs failed: {self.migration_stats['mcps_failed']}")
        print(f"Agents updated: {self.migration_stats['agents_updated']}")
        print(f"Agents failed: {self.migration_stats['agents_failed']}")
        print("="*60)


async def main():
    """
    Fonction principale du script de migration
    """
    import argparse
    
    parser = argparse.ArgumentParser(description='Migrate Pipedream data to Arcade.dev')
    parser.add_argument('--dry-run', action='store_true', default=True,
                       help='Run in dry-run mode (no changes made)')
    parser.add_argument('--execute', action='store_true', default=False,
                       help='Execute the migration (makes actual changes)')
    
    args = parser.parse_args()
    
    # Par sécurité, dry_run est True par défaut
    dry_run = not args.execute
    
    if not dry_run:
        print("⚠️  WARNING: This will make actual changes to your database!")
        print("⚠️  Make sure you have a backup before proceeding.")
        confirm = input("Type 'MIGRATE' to confirm: ")
        if confirm != 'MIGRATE':
            print("Migration cancelled.")
            return
    
    migrator = PipedreamToArcadeMigrator()
    await migrator.run_migration(dry_run=dry_run)


if __name__ == "__main__":
    asyncio.run(main())
