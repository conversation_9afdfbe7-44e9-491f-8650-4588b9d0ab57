import { type Agent } from "./_shims/index.js";
import * as Core from "./core.js";
import * as Errors from "./error.js";
import * as Pagination from "./pagination.js";
import { type OffsetPageParams, OffsetPageResponse } from "./pagination.js";
import * as Uploads from "./uploads.js";
import * as API from "./resources/index.js";
import { Auth, AuthAuthorizeParams, AuthConfirmUserParams, AuthRequest, AuthStatusParams, ConfirmUserRequest, ConfirmUserResponse } from "./resources/auth.js";
import { Health, HealthSchema } from "./resources/health.js";
import { CreateWorkerRequest, UpdateWorkerRequest, WorkerCreateParams, WorkerHealthResponse, WorkerListParams, WorkerResponse, WorkerResponsesOffsetPage, WorkerToolsParams, WorkerUpdateParams, Workers } from "./resources/workers.js";
import { Admin } from "./resources/admin/admin.js";
import { Cha<PERSON>, ChatMessage, ChatRequest, ChatResponse, Choice, Usage } from "./resources/chat/chat.js";
import { AuthorizeToolRequest, ExecuteToolRequest, ExecuteToolResponse, ToolAuthorizeParams, ToolDefinition, ToolDefinitionsOffsetPage, ToolExecuteParams, ToolExecution, ToolExecutionAttempt, ToolGetParams, ToolListParams, Tools, ValueSchema } from "./resources/tools/tools.js";
export interface ClientOptions {
    /**
     * API key used for authorization in header
     */
    apiKey?: string | undefined;
    /**
     * Override the default base URL for the API, e.g., "https://api.example.com/v2/"
     *
     * Defaults to process.env['ARCADE_BASE_URL'].
     */
    baseURL?: string | null | undefined;
    /**
     * The maximum amount of time (in milliseconds) that the client should wait for a response
     * from the server before timing out a single request.
     *
     * Note that request timeouts are retried by default, so in a worst-case scenario you may wait
     * much longer than this timeout before the promise succeeds or fails.
     *
     * @unit milliseconds
     */
    timeout?: number | undefined;
    /**
     * An HTTP agent used to manage HTTP(S) connections.
     *
     * If not provided, an agent will be constructed by default in the Node.js environment,
     * otherwise no agent is used.
     */
    httpAgent?: Agent | undefined;
    /**
     * Specify a custom `fetch` function implementation.
     *
     * If not provided, we use `node-fetch` on Node.js and otherwise expect that `fetch` is
     * defined globally.
     */
    fetch?: Core.Fetch | undefined;
    /**
     * The maximum number of times that the client will retry a request in case of a
     * temporary failure, like a network error or a 5XX error from the server.
     *
     * @default 2
     */
    maxRetries?: number | undefined;
    /**
     * Default headers to include with every request to the API.
     *
     * These can be removed in individual requests by explicitly setting the
     * header to `undefined` or `null` in request options.
     */
    defaultHeaders?: Core.Headers | undefined;
    /**
     * Default query parameters to include with every request to the API.
     *
     * These can be removed in individual requests by explicitly setting the
     * param to `undefined` in request options.
     */
    defaultQuery?: Core.DefaultQuery | undefined;
}
/**
 * API Client for interfacing with the Arcade API.
 */
export declare class Arcade extends Core.APIClient {
    #private;
    apiKey: string;
    private _options;
    /**
     * API Client for interfacing with the Arcade API.
     *
     * @param {string | undefined} [opts.apiKey=process.env['ARCADE_API_KEY'] ?? undefined]
     * @param {string} [opts.baseURL=process.env['ARCADE_BASE_URL'] ?? https://api.arcade.dev] - Override the default base URL for the API.
     * @param {number} [opts.timeout=1 minute] - The maximum amount of time (in milliseconds) the client will wait for a response before timing out.
     * @param {number} [opts.httpAgent] - An HTTP agent used to manage HTTP(s) connections.
     * @param {Core.Fetch} [opts.fetch] - Specify a custom `fetch` function implementation.
     * @param {number} [opts.maxRetries=2] - The maximum number of times the client will retry a request.
     * @param {Core.Headers} opts.defaultHeaders - Default headers to include with every request to the API.
     * @param {Core.DefaultQuery} opts.defaultQuery - Default query parameters to include with every request to the API.
     */
    constructor({ baseURL, apiKey, ...opts }?: ClientOptions);
    admin: API.Admin;
    auth: API.Auth;
    health: API.Health;
    chat: API.Chat;
    tools: API.Tools;
    workers: API.Workers;
    protected defaultQuery(): Core.DefaultQuery | undefined;
    protected defaultHeaders(opts: Core.FinalRequestOptions): Core.Headers;
    protected authHeaders(opts: Core.FinalRequestOptions): Core.Headers;
    protected stringifyQuery(query: Record<string, unknown>): string;
    static Arcade: typeof Arcade;
    static DEFAULT_TIMEOUT: number;
    static ArcadeError: typeof Errors.ArcadeError;
    static APIError: typeof Errors.APIError;
    static APIConnectionError: typeof Errors.APIConnectionError;
    static APIConnectionTimeoutError: typeof Errors.APIConnectionTimeoutError;
    static APIUserAbortError: typeof Errors.APIUserAbortError;
    static NotFoundError: typeof Errors.NotFoundError;
    static ConflictError: typeof Errors.ConflictError;
    static RateLimitError: typeof Errors.RateLimitError;
    static BadRequestError: typeof Errors.BadRequestError;
    static AuthenticationError: typeof Errors.AuthenticationError;
    static InternalServerError: typeof Errors.InternalServerError;
    static PermissionDeniedError: typeof Errors.PermissionDeniedError;
    static UnprocessableEntityError: typeof Errors.UnprocessableEntityError;
    static toFile: typeof Uploads.toFile;
    static fileFromPath: typeof Uploads.fileFromPath;
}
export declare namespace Arcade {
    export type RequestOptions = Core.RequestOptions;
    export import OffsetPage = Pagination.OffsetPage;
    export { type OffsetPageParams as OffsetPageParams, type OffsetPageResponse as OffsetPageResponse };
    export { Admin as Admin };
    export { Auth as Auth, type AuthRequest as AuthRequest, type ConfirmUserRequest as ConfirmUserRequest, type ConfirmUserResponse as ConfirmUserResponse, type AuthAuthorizeParams as AuthAuthorizeParams, type AuthConfirmUserParams as AuthConfirmUserParams, type AuthStatusParams as AuthStatusParams, };
    export { Health as Health, type HealthSchema as HealthSchema };
    export { Chat as Chat, type ChatMessage as ChatMessage, type ChatRequest as ChatRequest, type ChatResponse as ChatResponse, type Choice as Choice, type Usage as Usage, };
    export { Tools as Tools, type AuthorizeToolRequest as AuthorizeToolRequest, type ExecuteToolRequest as ExecuteToolRequest, type ExecuteToolResponse as ExecuteToolResponse, type ToolDefinition as ToolDefinition, type ToolExecution as ToolExecution, type ToolExecutionAttempt as ToolExecutionAttempt, type ValueSchema as ValueSchema, ToolDefinitionsOffsetPage as ToolDefinitionsOffsetPage, type ToolListParams as ToolListParams, type ToolAuthorizeParams as ToolAuthorizeParams, type ToolExecuteParams as ToolExecuteParams, type ToolGetParams as ToolGetParams, };
    export { Workers as Workers, type CreateWorkerRequest as CreateWorkerRequest, type UpdateWorkerRequest as UpdateWorkerRequest, type WorkerHealthResponse as WorkerHealthResponse, type WorkerResponse as WorkerResponse, WorkerResponsesOffsetPage as WorkerResponsesOffsetPage, type WorkerCreateParams as WorkerCreateParams, type WorkerUpdateParams as WorkerUpdateParams, type WorkerListParams as WorkerListParams, type WorkerToolsParams as WorkerToolsParams, };
    export type AuthorizationContext = API.AuthorizationContext;
    export type AuthorizationResponse = API.AuthorizationResponse;
    export type Error = API.Error;
}
export { toFile, fileFromPath } from "./uploads.js";
export { ArcadeError, APIError, APIConnectionError, APIConnectionTimeoutError, APIUserAbortError, NotFoundError, ConflictError, RateLimitError, BadRequestError, AuthenticationError, InternalServerError, PermissionDeniedError, UnprocessableEntityError, } from "./error.js";
export default Arcade;
//# sourceMappingURL=index.d.ts.map