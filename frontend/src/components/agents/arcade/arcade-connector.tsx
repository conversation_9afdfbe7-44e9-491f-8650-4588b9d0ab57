'use client';

import React, { useState, useEffect } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Loader2, ExternalLink, CheckCircle, AlertCircle, Zap } from 'lucide-react';
import { toast } from 'sonner';
import { useCreateConnectionToken, useArcadeToolDiscoveryForToolkit } from '@/hooks/react-query/arcade/use-arcade';
import type { ArcadeToolkit } from '@/hooks/react-query/arcade/use-arcade';

interface ArcadeConnectorProps {
  toolkit: ArcadeToolkit;
  selectedAgentId?: string;
  onConnectionSuccess?: (toolkit: ArcadeToolkit) => void;
  onConnectionError?: (error: string) => void;
}

export function ArcadeConnector({
  toolkit,
  selectedAgentId,
  onConnectionSuccess,
  onConnectionError
}: ArcadeConnectorProps) {
  const [isConnecting, setIsConnecting] = useState(false);
  const [connectionStatus, setConnectionStatus] = useState<'idle' | 'connecting' | 'connected' | 'error'>('idle');
  const [authUrl, setAuthUrl] = useState<string | null>(null);

  const createConnectionTokenMutation = useCreateConnectionToken();
  
  // Découvrir les outils du toolkit
  const { 
    data: toolsData, 
    isLoading: isLoadingTools,
    error: toolsError 
  } = useArcadeToolDiscoveryForToolkit(
    toolkit.name,
    undefined,
    true
  );

  const handleConnect = async () => {
    if (!toolkit) return;

    setIsConnecting(true);
    setConnectionStatus('connecting');

    try {
      const result = await createConnectionTokenMutation.mutateAsync({
        toolkit: toolkit.name
      });

      if (result.success && result.auth_url) {
        setAuthUrl(result.auth_url);
        
        // Ouvrir l'URL d'authentification dans une nouvelle fenêtre
        const authWindow = window.open(
          result.auth_url,
          'arcade-auth',
          'width=600,height=700,scrollbars=yes,resizable=yes'
        );

        if (authWindow) {
          // Surveiller la fermeture de la fenêtre d'auth
          const checkClosed = setInterval(() => {
            if (authWindow.closed) {
              clearInterval(checkClosed);
              // Vérifier le statut de la connexion
              checkConnectionStatus(result.auth_id);
            }
          }, 1000);

          // Timeout après 10 minutes
          setTimeout(() => {
            clearInterval(checkClosed);
            if (!authWindow.closed) {
              authWindow.close();
              setConnectionStatus('error');
              setIsConnecting(false);
              toast.error('Authentication timeout. Please try again.');
            }
          }, 10 * 60 * 1000);
        } else {
          // Fallback si popup bloqué
          toast.info('Please complete authentication in the new tab', {
            action: {
              label: 'Open Auth',
              onClick: () => window.open(result.auth_url, '_blank')
            }
          });
        }
      } else {
        throw new Error(result.error || 'Failed to create connection token');
      }
    } catch (error) {
      console.error('Connection error:', error);
      setConnectionStatus('error');
      setIsConnecting(false);
      
      const errorMessage = error instanceof Error ? error.message : 'Connection failed';
      toast.error(`Failed to connect to ${toolkit.display_name}: ${errorMessage}`);
      onConnectionError?.(errorMessage);
    }
  };

  const checkConnectionStatus = async (authId?: string) => {
    // TODO: Implémenter la vérification du statut d'authentification
    // Pour l'instant, on simule une connexion réussie
    setTimeout(() => {
      setConnectionStatus('connected');
      setIsConnecting(false);
      toast.success(`Successfully connected to ${toolkit.display_name}!`);
      onConnectionSuccess?.(toolkit);
    }, 2000);
  };

  const getStatusIcon = () => {
    switch (connectionStatus) {
      case 'connecting':
        return <Loader2 className="h-4 w-4 animate-spin" />;
      case 'connected':
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      case 'error':
        return <AlertCircle className="h-4 w-4 text-red-500" />;
      default:
        return <Zap className="h-4 w-4" />;
    }
  };

  const getStatusText = () => {
    switch (connectionStatus) {
      case 'connecting':
        return 'Connecting...';
      case 'connected':
        return 'Connected';
      case 'error':
        return 'Connection failed';
      default:
        return 'Connect';
    }
  };

  const isDisabled = isConnecting || connectionStatus === 'connected';

  return (
    <Card className="w-full">
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <div className="w-10 h-10 rounded-lg bg-gradient-to-br from-blue-500 to-purple-600 flex items-center justify-center">
              <span className="text-white font-semibold text-sm">
                {toolkit.display_name.charAt(0).toUpperCase()}
              </span>
            </div>
            <div>
              <CardTitle className="text-lg">{toolkit.display_name}</CardTitle>
              <CardDescription className="text-sm">
                {toolkit.description}
              </CardDescription>
            </div>
          </div>
          <Badge variant="secondary" className="text-xs">
            {toolkit.category}
          </Badge>
        </div>
      </CardHeader>

      <CardContent className="space-y-4">
        {/* Informations sur les outils */}
        <div className="flex items-center justify-between text-sm text-muted-foreground">
          <span>Available tools:</span>
          <span className="font-medium">
            {isLoadingTools ? (
              <Loader2 className="h-3 w-3 animate-spin inline" />
            ) : toolsError ? (
              <span className="text-red-500">Error loading</span>
            ) : (
              `${toolsData?.count || toolkit.tool_count || 0} tools`
            )}
          </span>
        </div>

        {/* Outils disponibles */}
        {toolsData && toolsData.tools && toolsData.tools.length > 0 && (
          <div className="space-y-2">
            <p className="text-xs font-medium text-muted-foreground">Sample tools:</p>
            <div className="flex flex-wrap gap-1">
              {toolsData.tools.slice(0, 3).map((tool) => (
                <Badge key={tool.name} variant="outline" className="text-xs">
                  {tool.name}
                </Badge>
              ))}
              {toolsData.tools.length > 3 && (
                <Badge variant="outline" className="text-xs">
                  +{toolsData.tools.length - 3} more
                </Badge>
              )}
            </div>
          </div>
        )}

        {/* Bouton de connexion */}
        <div className="flex items-center space-x-2">
          <Button
            onClick={handleConnect}
            disabled={isDisabled}
            className="flex-1"
            variant={connectionStatus === 'connected' ? 'secondary' : 'default'}
          >
            {getStatusIcon()}
            <span className="ml-2">{getStatusText()}</span>
          </Button>

          {authUrl && connectionStatus === 'connecting' && (
            <Button
              variant="outline"
              size="sm"
              onClick={() => window.open(authUrl, '_blank')}
            >
              <ExternalLink className="h-3 w-3" />
            </Button>
          )}
        </div>

        {/* Message d'état */}
        {connectionStatus === 'connecting' && (
          <p className="text-xs text-muted-foreground text-center">
            Complete the authentication in the popup window
          </p>
        )}

        {connectionStatus === 'connected' && (
          <p className="text-xs text-green-600 text-center">
            ✓ Successfully connected to {toolkit.display_name}
          </p>
        )}

        {connectionStatus === 'error' && (
          <p className="text-xs text-red-600 text-center">
            Failed to connect. Please try again.
          </p>
        )}
      </CardContent>
    </Card>
  );
}
