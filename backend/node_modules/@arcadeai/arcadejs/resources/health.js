"use strict";
// File generated from our OpenAPI spec by Stainless. See CONTRIBUTING.md for details.
Object.defineProperty(exports, "__esModule", { value: true });
exports.Health = void 0;
const resource_1 = require("../resource.js");
class Health extends resource_1.APIResource {
    /**
     * Engine Health
     */
    check(options) {
        return this._client.get('/v1/health', options);
    }
}
exports.Health = Health;
//# sourceMappingURL=health.js.map