// File generated from our OpenAPI spec by Stainless. See CONTRIBUTING.md for details.
import { APIResource } from "../../resource.mjs";
export class Secrets extends APIResource {
    /**
     * List all secrets that are visible to the caller
     */
    list(options) {
        return this._client.get('/v1/admin/secrets', options);
    }
    /**
     * Delete a secret by its ID
     */
    delete(secretId, options) {
        return this._client.delete(`/v1/admin/secrets/${secretId}`, {
            ...options,
            headers: { Accept: '*/*', ...options?.headers },
        });
    }
}
//# sourceMappingURL=secrets.mjs.map