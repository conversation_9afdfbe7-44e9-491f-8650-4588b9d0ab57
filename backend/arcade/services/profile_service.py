from typing import List, Optional, Dict, Any
from uuid import UUID, uuid4

from ..domain.entities import Profile
from ..domain.exceptions import ProfileNotFoundError, ProfileAlreadyExistsError
from ..repositories.profile_repository import SupabaseProfileRepository
from ..services.auth_service import AuthService
from ..protocols import Logger


class ProfileService:
    """
    Service de gestion des profils Arcade
    Compatible avec l'API Pipedream existante
    """
    
    def __init__(
        self, 
        profile_repository: SupabaseProfileRepository,
        auth_service: AuthService,
        logger: Logger
    ):
        self._profile_repo = profile_repository
        self._auth_service = auth_service
        self._logger = logger
    
    async def create_profile(
        self,
        account_id: UUID,
        profile_name: str,
        toolkit_name: str,
        app_name: str,
        description: Optional[str] = None,
        is_default: bool = False,
        oauth_app_id: Optional[str] = None,
        enabled_tools: Optional[List[str]] = None,
        external_user_id: Optional[str] = None
    ) -> Profile:
        """
        Crée un nouveau profil Arcade
        """
        try:
            self._logger.info(f"Creating profile {profile_name} for toolkit {toolkit_name}")
            
            # Si c'est le profil par défaut, désactiver les autres profils par défaut
            if is_default:
                await self._unset_default_profiles(account_id, toolkit_name)
            
            # Créer le profil
            profile = Profile(
                profile_id=uuid4(),
                account_id=account_id,
                profile_name=profile_name,
                toolkit_name=toolkit_name,
                app_name=app_name,
                description=description,
                is_default=is_default,
                enabled_tools=enabled_tools or [],
                external_user_id=external_user_id,
                oauth_app_id=oauth_app_id
            )
            
            # Sauvegarder en base
            created_profile = await self._profile_repo.create(profile)
            
            self._logger.info(f"Profile {profile_name} created successfully with ID {created_profile.profile_id}")
            return created_profile
            
        except ProfileAlreadyExistsError:
            raise
        except Exception as e:
            self._logger.error(f"Error creating profile: {str(e)}")
            raise
    
    async def get_profile(self, account_id: UUID, profile_id: UUID) -> Optional[Profile]:
        """
        Récupère un profil par ID
        """
        try:
            return await self._profile_repo.get_by_id(account_id, str(profile_id))
        except Exception as e:
            self._logger.error(f"Error getting profile: {str(e)}")
            return None
    
    async def get_profiles(
        self,
        account_id: UUID,
        toolkit_name: Optional[str] = None,
        is_active: Optional[bool] = None
    ) -> List[Profile]:
        """
        Récupère les profils d'un compte
        """
        try:
            return await self._profile_repo.get_by_account(account_id, toolkit_name, is_active)
        except Exception as e:
            self._logger.error(f"Error getting profiles: {str(e)}")
            return []
    
    async def update_profile(
        self,
        account_id: UUID,
        profile_id: UUID,
        profile_name: Optional[str] = None,
        display_name: Optional[str] = None,
        is_active: Optional[bool] = None,
        is_default: Optional[bool] = None,
        enabled_tools: Optional[List[str]] = None
    ) -> Profile:
        """
        Met à jour un profil
        """
        try:
            # Récupérer le profil existant
            profile = await self._profile_repo.get_by_id(account_id, str(profile_id))
            if not profile:
                raise ProfileNotFoundError(f"Profile {profile_id} not found")
            
            # Mettre à jour les champs
            if profile_name is not None:
                profile.profile_name = profile_name
            if is_active is not None:
                profile.is_active = is_active
            if enabled_tools is not None:
                profile.enabled_tools = enabled_tools
            
            # Gérer le profil par défaut
            if is_default is not None and is_default:
                await self._unset_default_profiles(account_id, profile.toolkit_name)
                profile.is_default = True
            elif is_default is not None:
                profile.is_default = False
            
            # Sauvegarder
            updated_profile = await self._profile_repo.update(profile)
            
            self._logger.info(f"Profile {profile_id} updated successfully")
            return updated_profile
            
        except ProfileNotFoundError:
            raise
        except Exception as e:
            self._logger.error(f"Error updating profile: {str(e)}")
            raise
    
    async def delete_profile(self, account_id: UUID, profile_id: UUID) -> bool:
        """
        Supprime un profil
        """
        try:
            success = await self._profile_repo.delete(account_id, str(profile_id))
            
            if success:
                self._logger.info(f"Profile {profile_id} deleted successfully")
            
            return success
            
        except Exception as e:
            self._logger.error(f"Error deleting profile: {str(e)}")
            return False
    
    async def get_profile_by_app(
        self,
        account_id: UUID,
        toolkit_name: str,
        profile_name: Optional[str] = None
    ) -> Optional[Profile]:
        """
        Récupère un profil par toolkit et nom (compatible Pipedream)
        """
        try:
            profiles = await self._profile_repo.get_by_account(account_id, toolkit_name)
            
            if profile_name:
                # Chercher par nom spécifique
                for profile in profiles:
                    if profile.profile_name == profile_name:
                        return profile
            else:
                # Retourner le profil par défaut ou le premier
                for profile in profiles:
                    if profile.is_default:
                        return profile
                
                # Si pas de profil par défaut, retourner le premier actif
                for profile in profiles:
                    if profile.is_active:
                        return profile
            
            return None
            
        except Exception as e:
            self._logger.error(f"Error getting profile by app: {str(e)}")
            return None
    
    async def get_enabled_tools_for_agent_profile(
        self,
        agent_id: str,
        profile_id: str,
        user_id: str
    ) -> List[str]:
        """
        Récupère les outils activés pour un profil d'agent
        Compatible avec l'API Pipedream existante
        """
        try:
            from services.supabase import DBConnection
            from agent.versioning.facade import VersionManagerFacade
            
            db = DBConnection()
            client = await db.client
            version_manager = VersionManagerFacade()
            
            # Récupérer l'agent
            agent_result = await client.table('agents').select('*').eq('agent_id', agent_id).eq('account_id', user_id).execute()
            if not agent_result.data:
                return []

            agent = agent_result.data[0]
            
            # Récupérer les MCPs de l'agent et de la version
            agent_custom_mcps = agent.get('custom_mcps', [])
            version_custom_mcps = []

            if agent.get('current_version_id'):
                try:
                    version_dict = await version_manager.get_version(
                        agent_id=agent_id,
                        version_id=agent['current_version_id'],
                        user_id=user_id
                    )
                    version_custom_mcps = version_dict.get('custom_mcps', [])
                except Exception:
                    pass
            
            # Chercher le MCP Arcade correspondant
            arcade_mcp = None
            
            # D'abord dans les MCPs de version
            for mcp in version_custom_mcps:
                mcp_type = mcp.get('type')
                mcp_config = mcp.get('config', {})
                mcp_profile_id = mcp_config.get('profile_id')
                
                if mcp_type == 'arcade' and mcp_profile_id == profile_id:
                    arcade_mcp = mcp
                    break

            # Puis dans les MCPs d'agent
            if not arcade_mcp:
                for mcp in agent_custom_mcps:
                    mcp_type = mcp.get('type')
                    mcp_config = mcp.get('config', {})
                    mcp_profile_id = mcp_config.get('profile_id')
                    
                    if mcp_type == 'arcade' and mcp_profile_id == profile_id:
                        arcade_mcp = mcp
                        break

            if not arcade_mcp:
                return []
            
            # Récupérer les outils activés
            enabled_tools = arcade_mcp.get('enabledTools', arcade_mcp.get('enabled_tools', []))
            self._logger.info(f"[PROFILE {profile_id}] Found Arcade MCP with {len(enabled_tools)} enabled tools: {enabled_tools}")
            return enabled_tools
            
        except Exception as e:
            self._logger.error(f"Error getting enabled tools for agent profile: {str(e)}")
            return []
    
    async def update_agent_profile_tools(
        self,
        agent_id: str,
        profile_id: str,
        user_id: str,
        enabled_tools: List[str]
    ) -> Dict[str, Any]:
        """
        Met à jour les outils d'un profil d'agent
        Compatible avec l'API Pipedream existante
        """
        try:
            from services.supabase import DBConnection
            from agent.versioning.facade import version_manager
            import copy
            
            db = DBConnection()
            from agent.versioning.infrastructure.dependencies import set_db_connection
            set_db_connection(db)
            client = await db.client
            
            # Récupérer l'agent
            agent_result = await client.table('agents').select('*').eq('agent_id', agent_id).eq('account_id', user_id).execute()
            if not agent_result.data:
                raise ValueError("Agent not found")
            
            agent = agent_result.data[0]
            
            # Récupérer les données de version actuelle
            current_version_data = None
            if agent.get('current_version_id'):
                try:
                    current_version_data = await version_manager.get_version(
                        agent_id=agent_id,
                        version_id=agent['current_version_id'],
                        user_id=user_id
                    )
                except Exception:
                    pass
            
            # Récupérer le profil Arcade
            profile = await self.get_profile(user_id, profile_id)
            if not profile:
                raise ValueError("Profile not found")
            
            # Préparer les données pour la nouvelle version
            if current_version_data:
                system_prompt = current_version_data.get('system_prompt', '')
                configured_mcps = current_version_data.get('configured_mcps', [])
                agentpress_tools = current_version_data.get('agentpress_tools', {})
                current_custom_mcps = current_version_data.get('custom_mcps', [])
            else:
                system_prompt = agent.get('system_prompt', '')
                configured_mcps = agent.get('configured_mcps', [])
                agentpress_tools = agent.get('agentpress_tools', {})
                current_custom_mcps = agent.get('custom_mcps', [])
            
            updated_custom_mcps = copy.deepcopy(current_custom_mcps)
            
            # Normaliser les noms des outils activés
            for mcp in updated_custom_mcps:
                if 'enabled_tools' in mcp and 'enabledTools' not in mcp:
                    mcp['enabledTools'] = mcp['enabled_tools']
                elif 'enabledTools' not in mcp and 'enabled_tools' not in mcp:
                    mcp['enabledTools'] = []

            # Chercher et mettre à jour le MCP Arcade
            found_match = False
            for mcp in updated_custom_mcps:
                if (mcp.get('type') == 'arcade' and 
                    mcp.get('config', {}).get('profile_id') == profile_id):                
                    mcp['enabledTools'] = enabled_tools
                    mcp['enabled_tools'] = enabled_tools
                    found_match = True
                    break
            
            # Si pas trouvé, créer un nouveau MCP Arcade
            if not found_match:
                new_mcp_config = {
                    "name": profile.app_name,
                    "type": "arcade",
                    "config": {
                        "url": "https://api.arcade.dev/mcp",
                        "toolkit_name": profile.toolkit_name,
                        "user_id": user_id,
                        "profile_id": profile_id,
                        "headers": {
                            "X-Arcade-Toolkit": profile.toolkit_name,
                            "X-Arcade-User-ID": user_id,
                            "X-Arcade-Profile-ID": profile_id
                        }
                    },
                    "enabledTools": enabled_tools,
                    "enabled_tools": enabled_tools
                }
                updated_custom_mcps.append(new_mcp_config)
            
            # Créer une nouvelle version
            new_version = await version_manager.create_version(
                agent_id=agent_id,
                user_id=user_id,
                system_prompt=system_prompt,
                configured_mcps=configured_mcps,
                custom_mcps=updated_custom_mcps,
                agentpress_tools=agentpress_tools,
                change_description=f"Updated {profile.app_name} tools"
            )
            
            # Mettre à jour l'agent
            update_result = await client.table('agents').update({
                'custom_mcps': updated_custom_mcps,
                'current_version_id': new_version['version_id']
            }).eq('agent_id', agent_id).execute()
            
            if not update_result.data:
                raise ValueError("Failed to update agent configuration")
            
            return {
                'success': True,
                'enabled_tools': enabled_tools,
                'total_tools': len(enabled_tools),
                'version_id': new_version['version_id'],
                'version_name': new_version['version_name']
            }
            
        except Exception as e:
            self._logger.error(f"Error updating agent profile tools: {str(e)}")
            raise
    
    async def _unset_default_profiles(self, account_id: UUID, toolkit_name: str):
        """
        Désactive le statut par défaut des autres profils du même toolkit
        """
        try:
            profiles = await self._profile_repo.get_by_account(account_id, toolkit_name)
            
            for profile in profiles:
                if profile.is_default:
                    profile.is_default = False
                    await self._profile_repo.update(profile)
                    
        except Exception as e:
            self._logger.error(f"Error unsetting default profiles: {str(e)}")
