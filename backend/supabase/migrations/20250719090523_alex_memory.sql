-- 1. Create the primary table for storing memories
CREATE TABLE IF NOT EXISTS alex_memories (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    account_id UUID NOT NULL REFERENCES basejump.accounts(id) ON DELETE CASCADE,
    memory_type TEXT NOT NULL CHECK (memory_type IN (
        'personal_info', 'project', 'preference', 'achievement',
        'challenge', 'goal', 'context', 'interaction'
    )),
    title TEXT NOT NULL,
    content TEXT NOT NULL,
    metadata JSONB DEFAULT '{}',
    importance INTEGER DEFAULT 3 CHECK (importance >= 1 AND importance <= 5),
    tags TEXT[] DEFAULT '{}',
    status TEXT DEFAULT 'active' CHECK (status IN ('active', 'archived', 'outdated')),
    fts tsvector GENERATED ALWAYS AS (to_tsvector('french', title || ' ' || content)) STORED,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    last_referenced_at TIMESTAMPTZ DEFAULT NOW()
);

-- 2. Add comments for clarity
COMMENT ON TABLE alex_memories IS 'Stores memories Alex has about each user to build lasting relationships.';
COMMENT ON COLUMN alex_memories.memory_type IS 'The type of memory: personal_info, project, preference, achievement, challenge, goal, context, interaction.';
COMMENT ON COLUMN alex_memories.importance IS 'The importance of the memory, from 1 (low) to 5 (very important).';
COMMENT ON COLUMN alex_memories.tags IS 'Tags for easy searching and categorization.';
COMMENT ON COLUMN alex_memories.last_referenced_at IS 'The last time this memory was used by Alex.';
COMMENT ON COLUMN alex_memories.fts IS 'Full-text search vector for title and content.';

-- 3. Create optimized indexes
CREATE INDEX IF NOT EXISTS idx_alex_memories_account_id ON alex_memories(account_id);
CREATE INDEX IF NOT EXISTS idx_alex_memories_type ON alex_memories(memory_type);
CREATE INDEX IF NOT EXISTS idx_alex_memories_status_importance ON alex_memories(status, importance DESC);
CREATE INDEX IF NOT EXISTS idx_alex_memories_last_referenced ON alex_memories(last_referenced_at DESC);
CREATE INDEX IF NOT EXISTS idx_alex_memories_fts ON alex_memories USING GIN(fts);
CREATE INDEX IF NOT EXISTS idx_alex_memories_tags ON alex_memories USING GIN(tags);

-- 4. Set up updated_at trigger function
CREATE OR REPLACE FUNCTION set_current_timestamp_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    IF NEW IS DISTINCT FROM OLD THEN
        NEW.updated_at = NOW();
    END IF;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trigger_alex_memories_updated_at
    BEFORE UPDATE ON alex_memories
    FOR EACH ROW
    EXECUTE FUNCTION set_current_timestamp_updated_at();

-- 5. Enable Row Level Security (RLS)
ALTER TABLE alex_memories ENABLE ROW LEVEL SECURITY;

-- Security helper function to get the user's account_id efficiently
CREATE OR REPLACE FUNCTION get_my_account_id()
RETURNS UUID AS $$
DECLARE
    user_account_id UUID;
BEGIN
    SELECT account_id INTO user_account_id
    FROM basejump.account_user
    WHERE user_id = auth.uid()
    LIMIT 1;
    RETURN user_account_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 6. Define RLS Policies using the helper function for better performance
DROP POLICY IF EXISTS "Users can only access their own memories" ON alex_memories;
CREATE POLICY "Users can only access their own memories" ON alex_memories
    FOR ALL
    USING (account_id = get_my_account_id())
    WITH CHECK (account_id = get_my_account_id());

-- 7. Create a view for recent and important memories
CREATE OR REPLACE VIEW alex_recent_memories AS
SELECT
    am.id,
    am.account_id,
    am.memory_type,
    am.title,
    am.content,
    am.importance,
    am.tags,
    am.last_referenced_at,
    ba.name as account_name
FROM alex_memories am
JOIN basejump.accounts ba ON am.account_id = ba.id
WHERE am.status = 'active'
ORDER BY am.importance DESC, am.last_referenced_at DESC;

-- 8. Create a performant search function using the new fts column
CREATE OR REPLACE FUNCTION search_alex_memories(
    p_account_id UUID,
    p_query TEXT,
    p_limit INTEGER DEFAULT 10
)
RETURNS TABLE (
    id UUID,
    memory_type TEXT,
    title TEXT,
    content TEXT,
    importance INTEGER,
    tags TEXT[],
    created_at TIMESTAMPTZ,
    relevance REAL
) AS $$
BEGIN
    RETURN QUERY
    SELECT
        am.id,
        am.memory_type,
        am.title,
        am.content,
        am.importance,
        am.tags,
        am.created_at,
        ts_rank(am.fts, plainto_tsquery('french', p_query)) as relevance
    FROM alex_memories am
    WHERE
        am.account_id = p_account_id
        AND am.status = 'active'
        AND (
            am.fts @@ plainto_tsquery('french', p_query)
            OR p_query = ANY(am.tags)
        )
    ORDER BY relevance DESC, am.importance DESC
    LIMIT p_limit;
END;
$$ LANGUAGE plpgsql STABLE;
