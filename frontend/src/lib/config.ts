// Environment mode types
export enum EnvMode {
  LOCAL = 'local',
  STAGING = 'staging',
  PRODUCTION = 'production',
}

// Subscription tier structure
export interface SubscriptionTierData {
  priceId: string;
  name: string;
}

// Subscription tiers structure
export interface SubscriptionTiers {
  FREE: SubscriptionTierData;
  TIER_2_20: SubscriptionTierData;
  TIER_6_50: SubscriptionTierData;
  TIER_12_100: SubscriptionTierData;
  TIER_50_400: SubscriptionTierData;
  // Yearly plans with 15% discount
  TIER_2_20_YEARLY: SubscriptionTierData;
  TIER_6_50_YEARLY: SubscriptionTierData;
  TIER_12_100_YEARLY: SubscriptionTierData;
  TIER_50_400_YEARLY: SubscriptionTierData;
}

// Configuration object
interface Config {
  ENV_MODE: EnvMode;
  IS_LOCAL: boolean;
  SUBSCRIPTION_TIERS: SubscriptionTiers;
}

// Production tier IDs
const PROD_TIERS: SubscriptionTiers = {
  FREE: {
    priceId: 'price_1RTkhuG7JHKhLHHmc6qBpRVh',
    name: 'Découverte',
  },
  TIER_2_20: {
    priceId: 'price_1RbMjsG7JHKhLHHmZMKc3Lqg',
    name: 'Étudiant',
  },
  TIER_6_50: {
    priceId: 'price_1RTknsG7JHKhLHHmjaRztRk5',
    name: 'Essentiel',
  },
  TIER_12_100: {
    priceId: 'price_1RTkoCG7JHKhLHHm3GpappDW',
    name: 'Pro',
  },
  TIER_50_400: {
    priceId: 'price_1RTkoTG7JHKhLHHmVYDunJ8i',
    name: 'Entreprise',
  },
  // Yearly plans with 15% discount (12x monthly price with 15% off)
  TIER_2_20_YEARLY: {
    priceId: 'price_1RlnH1G7JHKhLHHm5U6Eu1o6',
    name: '2h/$204/year',
  },
  TIER_6_50_YEARLY: {
    priceId: 'price_1RlnIeG7JHKhLHHmmLNLVHGe',
    name: '6h/$510/year',
  },
  TIER_12_100_YEARLY: {
    priceId: 'price_1RlnIrG7JHKhLHHm4o55yrak',
    name: '12h/$1020/year',
  },
  TIER_50_400_YEARLY: {
    priceId: 'price_1RlnJ2G7JHKhLHHm9infhoW4',
    name: '50h/$4080/year',
  },
} as const;

// Staging tier IDs
const STAGING_TIERS: SubscriptionTiers = {
  FREE: {
    priceId: 'price_1Rln3l4UtNzkbXtjNFNT5E8G',
    name: 'Free',
  },
  TIER_2_20: {
    priceId: 'price_1Rln4L4UtNzkbXtj0gIY3QMy',
    name: '2h/$20',
  },
  TIER_6_50: {
    priceId: 'price_1Rln4X4UtNzkbXtjNCjv49WU',
    name: '6h/$50',
  },
  TIER_12_100: {
    priceId: 'price_1Rln4g4UtNzkbXtjrA4wlgOf',
    name: '12h/$100',
  },
  TIER_50_400: {
    priceId: 'price_1Rln594UtNzkbXtjErmIRilq',
    name: '50h/$400',
  },
  // Yearly plans with 15% discount (12x monthly price with 15% off)
  TIER_2_20_YEARLY: {
    priceId: 'price_1Rln8p4UtNzkbXtjVBqduqtR',
    name: '2h/$204/year',
  },
  TIER_6_50_YEARLY: {
    priceId: 'price_1Rln9R4UtNzkbXtjZyq1J85W',
    name: '6h/$510/year',
  },
  TIER_12_100_YEARLY: {
    priceId: 'price_1Rln9z4UtNzkbXtjRJE6zcaK',
    name: '12h/$1020/year',
  },
  TIER_50_400_YEARLY: {
    priceId: 'price_1RlnAT4UtNzkbXtjUXSEOLEM',
    name: '50h/$4080/year',
  },
} as const;

// Determine the environment mode from environment variables
const getEnvironmentMode = (): EnvMode => {
  // Get the environment mode from the environment variable, if set
  const envMode = process.env.NEXT_PUBLIC_ENV_MODE?.toLowerCase();

  // First check if the environment variable is explicitly set
  if (envMode) {
    if (envMode === EnvMode.LOCAL) {
      console.log('Using explicitly set LOCAL environment mode');
      return EnvMode.LOCAL;
    } else if (envMode === EnvMode.STAGING) {
      console.log('Using explicitly set STAGING environment mode');
      return EnvMode.STAGING;
    } else if (envMode === EnvMode.PRODUCTION) {
      console.log('Using explicitly set PRODUCTION environment mode');
      return EnvMode.PRODUCTION;
    }
  }

  // If no valid environment mode is set, fall back to defaults based on NODE_ENV
  if (process.env.NODE_ENV === 'development') {
    console.log('Defaulting to LOCAL environment mode in development');
    return EnvMode.LOCAL;
  } else {
    console.log('Defaulting to PRODUCTION environment mode');
    return EnvMode.PRODUCTION;
  }
};

// Get the environment mode once to ensure consistency
const currentEnvMode = getEnvironmentMode();

// Create the config object
export const config: Config = {
  ENV_MODE: currentEnvMode,
  IS_LOCAL: currentEnvMode === EnvMode.LOCAL,
  SUBSCRIPTION_TIERS:
    currentEnvMode === EnvMode.STAGING ? STAGING_TIERS : PROD_TIERS,
};

// Helper function to check if we're in local mode (for component conditionals)
export const isLocalMode = (): boolean => {
  return config.IS_LOCAL;
};

// Export subscription tier type for typing elsewhere
export type SubscriptionTier = keyof typeof PROD_TIERS;
