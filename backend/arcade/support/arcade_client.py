import os
import async<PERSON>
from typing import Dict, Any, Optional, List
from arcadepy import Arcade
from ..domain.exceptions import ArcadeClientException, AuthenticationException


class ArcadeClient:
    """
    Client wrapper pour l'API Arcade.dev
    Gère l'authentification et les appels API
    """
    
    def __init__(self):
        self.api_key = os.getenv("ARCADE_API_KEY")
        if not self.api_key:
            raise AuthenticationException("ARCADE_API_KEY environment variable is required")
        
        self._client = Arcade(api_key=self.api_key)
        self._session_cache = {}  # Cache pour les sessions utilisateur
    
    async def list_toolkits(
        self, 
        category: Optional[str] = None,
        search: Optional[str] = None,
        limit: int = 50
    ) -> Dict[str, Any]:
        """Liste les toolkits disponibles"""
        try:
            # Arcade.dev utilise une approche différente - on liste les outils par toolkit
            popular_toolkits = [
                "gmail", "slack", "github", "notion", "google-drive", 
                "calendar", "hubspot", "salesforce", "jira", "asana"
            ]
            
            toolkits = []
            for toolkit_name in popular_toolkits:
                try:
                    tools = self._client.tools.list(toolkit=toolkit_name, limit=10)
                    if tools:
                        toolkits.append({
                            "name": toolkit_name,
                            "display_name": toolkit_name.replace("-", " ").title(),
                            "description": f"Tools for {toolkit_name}",
                            "tool_count": len(tools),
                            "category": self._get_toolkit_category(toolkit_name)
                        })
                except Exception:
                    continue  # Skip toolkits that aren't available
            
            # Filter by search if provided
            if search:
                search_lower = search.lower()
                toolkits = [
                    t for t in toolkits 
                    if search_lower in t["name"].lower() or search_lower in t["display_name"].lower()
                ]
            
            # Filter by category if provided
            if category:
                toolkits = [t for t in toolkits if t["category"] == category]
            
            return {
                "toolkits": toolkits[:limit],
                "total": len(toolkits),
                "page": 1,
                "has_more": len(toolkits) > limit
            }
            
        except Exception as e:
            raise ArcadeClientException(f"Failed to list toolkits: {str(e)}")
    
    async def get_toolkit_tools(self, toolkit_name: str, user_id: str) -> List[Dict[str, Any]]:
        """Récupère les outils d'un toolkit pour un utilisateur"""
        try:
            tools = self._client.tools.list(toolkit=toolkit_name, limit=100)
            
            tool_list = []
            for tool in tools:
                tool_info = {
                    "name": tool.name,
                    "description": tool.description,
                    "input_schema": getattr(tool, 'input_schema', {}),
                    "requires_auth": bool(getattr(tool, 'requirements', {}).get('authorization')),
                    "toolkit": toolkit_name
                }
                tool_list.append(tool_info)
            
            return tool_list
            
        except Exception as e:
            raise ArcadeClientException(f"Failed to get toolkit tools: {str(e)}")
    
    async def start_auth_flow(self, user_id: str, toolkit_name: str) -> Dict[str, Any]:
        """Démarre un flux d'authentification pour un utilisateur"""
        try:
            # Get tools for the toolkit to determine required scopes
            tools = self._client.tools.list(toolkit=toolkit_name)
            
            # Collect all required scopes
            scopes = set()
            provider = None
            
            for tool in tools:
                if hasattr(tool, 'requirements') and tool.requirements:
                    auth_req = tool.requirements.get('authorization', {})
                    if auth_req:
                        oauth2_info = auth_req.get('oauth2', {})
                        if oauth2_info:
                            tool_scopes = oauth2_info.get('scopes', [])
                            scopes.update(tool_scopes)
                            if not provider:
                                provider = self._get_provider_for_toolkit(toolkit_name)
            
            if not provider:
                raise ArcadeClientException(f"No auth provider found for toolkit: {toolkit_name}")
            
            # Start authentication with Arcade
            auth_response = self._client.auth.start(
                user_id=user_id,
                provider=provider,
                scopes=list(scopes) if scopes else None
            )
            
            return {
                "success": True,
                "auth_id": auth_response.id,
                "url": auth_response.url,
                "status": auth_response.status,
                "user_id": user_id,
                "toolkit": toolkit_name,
                "provider": provider
            }
            
        except Exception as e:
            raise ArcadeClientException(f"Failed to start auth flow: {str(e)}")
    
    async def check_auth_status(self, auth_id: str) -> Dict[str, Any]:
        """Vérifie le statut d'une authentification"""
        try:
            # Note: Cette méthode dépend de l'API Arcade pour vérifier le statut
            # En attendant, on peut implémenter un polling ou utiliser des webhooks
            return {
                "auth_id": auth_id,
                "status": "pending",  # ou "completed", "failed"
                "message": "Authentication status check not yet implemented"
            }
        except Exception as e:
            raise ArcadeClientException(f"Failed to check auth status: {str(e)}")
    
    async def execute_tool(
        self, 
        tool_name: str, 
        user_id: str, 
        input_data: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Exécute un outil Arcade"""
        try:
            response = self._client.tools.execute(
                tool_name=tool_name,
                input=input_data,
                user_id=user_id
            )
            
            return {
                "success": True,
                "output": response.output,
                "tool_name": tool_name,
                "execution_id": getattr(response, 'id', None)
            }
            
        except Exception as e:
            raise ArcadeClientException(f"Failed to execute tool {tool_name}: {str(e)}")
    
    def _get_toolkit_category(self, toolkit_name: str) -> str:
        """Détermine la catégorie d'un toolkit"""
        categories = {
            "gmail": "communication",
            "slack": "communication", 
            "github": "development",
            "notion": "productivity",
            "google-drive": "storage",
            "calendar": "productivity",
            "hubspot": "crm",
            "salesforce": "crm",
            "jira": "project-management",
            "asana": "project-management"
        }
        return categories.get(toolkit_name, "other")
    
    def _get_provider_for_toolkit(self, toolkit_name: str) -> str:
        """Détermine le provider OAuth pour un toolkit"""
        providers = {
            "gmail": "google",
            "google-drive": "google",
            "calendar": "google",
            "slack": "slack",
            "github": "github",
            "notion": "notion",
            "hubspot": "hubspot",
            "salesforce": "salesforce",
            "jira": "atlassian",
            "asana": "asana"
        }
        return providers.get(toolkit_name, "oauth2")
    
    async def close(self):
        """Ferme le client"""
        # Arcade client doesn't need explicit closing in current version
        pass
