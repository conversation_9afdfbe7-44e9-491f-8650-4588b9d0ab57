{"version": 3, "file": "workers.d.ts", "sourceRoot": "", "sources": ["../src/resources/workers.ts"], "names": [], "mappings": "AAEA,OAAO,EAAE,WAAW,EAAE,MAAM,aAAa,CAAC;AAE1C,OAAO,KAAK,IAAI,MAAM,SAAS,CAAC;AAChC,OAAO,KAAK,QAAQ,MAAM,eAAe,CAAC;AAC1C,OAAO,EAAE,yBAAyB,EAAE,MAAM,eAAe,CAAC;AAC1D,OAAO,EAAE,UAAU,EAAE,KAAK,gBAAgB,EAAE,MAAM,eAAe,CAAC;AAElE,qBAAa,OAAQ,SAAQ,WAAW;IACtC;;OAEG;IACH,MAAM,CAAC,IAAI,EAAE,kBAAkB,EAAE,OAAO,CAAC,EAAE,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,UAAU,CAAC,cAAc,CAAC;IAIhG;;OAEG;IACH,MAAM,CACJ,EAAE,EAAE,MAAM,EACV,IAAI,EAAE,kBAAkB,EACxB,OAAO,CAAC,EAAE,IAAI,CAAC,cAAc,GAC5B,IAAI,CAAC,UAAU,CAAC,cAAc,CAAC;IAIlC;;OAEG;IACH,IAAI,CACF,KAAK,CAAC,EAAE,gBAAgB,EACxB,OAAO,CAAC,EAAE,IAAI,CAAC,cAAc,GAC5B,IAAI,CAAC,WAAW,CAAC,yBAAyB,EAAE,cAAc,CAAC;IAC9D,IAAI,CAAC,OAAO,CAAC,EAAE,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,WAAW,CAAC,yBAAyB,EAAE,cAAc,CAAC;IAWhG;;OAEG;IACH,MAAM,CAAC,EAAE,EAAE,MAAM,EAAE,OAAO,CAAC,EAAE,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC;IAOxE;;OAEG;IACH,GAAG,CAAC,EAAE,EAAE,MAAM,EAAE,OAAO,CAAC,EAAE,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,UAAU,CAAC,cAAc,CAAC;IAI/E;;OAEG;IACH,MAAM,CAAC,EAAE,EAAE,MAAM,EAAE,OAAO,CAAC,EAAE,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,UAAU,CAAC,oBAAoB,CAAC;IAIxF;;OAEG;IACH,KAAK,CACH,EAAE,EAAE,MAAM,EACV,KAAK,CAAC,EAAE,iBAAiB,EACzB,OAAO,CAAC,EAAE,IAAI,CAAC,cAAc,GAC5B,IAAI,CAAC,WAAW,CAAC,yBAAyB,EAAE,QAAQ,CAAC,cAAc,CAAC;IACvE,KAAK,CACH,EAAE,EAAE,MAAM,EACV,OAAO,CAAC,EAAE,IAAI,CAAC,cAAc,GAC5B,IAAI,CAAC,WAAW,CAAC,yBAAyB,EAAE,QAAQ,CAAC,cAAc,CAAC;CAcxE;AAED,qBAAa,yBAA0B,SAAQ,UAAU,CAAC,cAAc,CAAC;CAAG;AAE5E,MAAM,WAAW,mBAAmB;IAClC,EAAE,EAAE,MAAM,CAAC;IAEX,OAAO,CAAC,EAAE,OAAO,CAAC;IAElB,IAAI,CAAC,EAAE,mBAAmB,CAAC,IAAI,CAAC;IAEhC,GAAG,CAAC,EAAE,mBAAmB,CAAC,GAAG,CAAC;IAE9B,IAAI,CAAC,EAAE,MAAM,CAAC;CACf;AAED,yBAAiB,mBAAmB,CAAC;IACnC,UAAiB,IAAI;QACnB,KAAK,EAAE,MAAM,CAAC;QAEd,MAAM,EAAE,MAAM,CAAC;QAEf,OAAO,EAAE,MAAM,CAAC;QAEhB,GAAG,EAAE,MAAM,CAAC;KACb;IAED,UAAiB,GAAG;QAClB,KAAK,EAAE,MAAM,CAAC;QAEd,OAAO,EAAE,MAAM,CAAC;QAEhB,GAAG,EAAE,MAAM,CAAC;KACb;CACF;AAED,MAAM,WAAW,mBAAmB;IAClC,OAAO,CAAC,EAAE,OAAO,CAAC;IAElB,IAAI,CAAC,EAAE,mBAAmB,CAAC,IAAI,CAAC;IAEhC,GAAG,CAAC,EAAE,mBAAmB,CAAC,GAAG,CAAC;CAC/B;AAED,yBAAiB,mBAAmB,CAAC;IACnC,UAAiB,IAAI;QACnB,KAAK,CAAC,EAAE,MAAM,CAAC;QAEf,MAAM,CAAC,EAAE,MAAM,CAAC;QAEhB,OAAO,CAAC,EAAE,MAAM,CAAC;QAEjB,GAAG,CAAC,EAAE,MAAM,CAAC;KACd;IAED,UAAiB,GAAG;QAClB,KAAK,CAAC,EAAE,MAAM,CAAC;QAEf,OAAO,CAAC,EAAE,MAAM,CAAC;QAEjB,GAAG,CAAC,EAAE,MAAM,CAAC;KACd;CACF;AAED,MAAM,WAAW,oBAAoB;IACnC,EAAE,CAAC,EAAE,MAAM,CAAC;IAEZ,OAAO,CAAC,EAAE,OAAO,CAAC;IAElB,OAAO,CAAC,EAAE,OAAO,CAAC;IAElB,OAAO,CAAC,EAAE,MAAM,CAAC;CAClB;AAED,MAAM,WAAW,cAAc;IAC7B,EAAE,CAAC,EAAE,MAAM,CAAC;IAEZ,OAAO,CAAC,EAAE,cAAc,CAAC,OAAO,CAAC;IAEjC,OAAO,CAAC,EAAE,OAAO,CAAC;IAElB,IAAI,CAAC,EAAE,cAAc,CAAC,IAAI,CAAC;IAE3B,GAAG,CAAC,EAAE,cAAc,CAAC,GAAG,CAAC;IAEzB,GAAG,CAAC,EAAE,cAAc,CAAC,GAAG,CAAC;IAEzB,IAAI,CAAC,EAAE,MAAM,GAAG,KAAK,GAAG,SAAS,CAAC;CACnC;AAED,yBAAiB,cAAc,CAAC;IAC9B,UAAiB,OAAO;QACtB,EAAE,CAAC,EAAE,MAAM,CAAC;QAEZ,IAAI,CAAC,EAAE,QAAQ,GAAG,QAAQ,GAAG,SAAS,GAAG,SAAS,CAAC;KACpD;IAED,UAAiB,IAAI;QACnB,KAAK,CAAC,EAAE,MAAM,CAAC;QAEf,MAAM,CAAC,EAAE,IAAI,CAAC,MAAM,CAAC;QAErB,OAAO,CAAC,EAAE,MAAM,CAAC;QAEjB,GAAG,CAAC,EAAE,MAAM,CAAC;KACd;IAED,UAAiB,IAAI,CAAC;QACpB,UAAiB,MAAM;YACrB,OAAO,CAAC,EAAE,QAAQ,GAAG,QAAQ,GAAG,SAAS,GAAG,SAAS,CAAC;YAEtD,QAAQ,CAAC,EAAE,OAAO,CAAC;YAEnB,MAAM,CAAC,EAAE,OAAO,CAAC;YAEjB,IAAI,CAAC,EAAE,MAAM,CAAC;YAEd,KAAK,CAAC,EAAE,MAAM,CAAC;SAChB;KACF;IAED,UAAiB,GAAG;QAClB,KAAK,CAAC,EAAE,MAAM,CAAC;QAEf,OAAO,CAAC,EAAE,MAAM,CAAC;QAEjB,GAAG,CAAC,EAAE,MAAM,CAAC;KACd;IAED,UAAiB,GAAG;QAClB,KAAK,CAAC,EAAE,MAAM,CAAC;QAEf,MAAM,CAAC,EAAE,GAAG,CAAC,MAAM,CAAC;QAEpB,OAAO,CAAC,EAAE,MAAM,CAAC;QAEjB,GAAG,CAAC,EAAE,MAAM,CAAC;KACd;IAED,UAAiB,GAAG,CAAC;QACnB,UAAiB,MAAM;YACrB,OAAO,CAAC,EAAE,QAAQ,GAAG,QAAQ,GAAG,SAAS,GAAG,SAAS,CAAC;YAEtD,QAAQ,CAAC,EAAE,OAAO,CAAC;YAEnB,MAAM,CAAC,EAAE,OAAO,CAAC;YAEjB,IAAI,CAAC,EAAE,MAAM,CAAC;YAEd,KAAK,CAAC,EAAE,MAAM,CAAC;SAChB;KACF;CACF;AAED,MAAM,WAAW,kBAAkB;IACjC,EAAE,EAAE,MAAM,CAAC;IAEX,OAAO,CAAC,EAAE,OAAO,CAAC;IAElB,IAAI,CAAC,EAAE,kBAAkB,CAAC,IAAI,CAAC;IAE/B,GAAG,CAAC,EAAE,kBAAkB,CAAC,GAAG,CAAC;IAE7B,IAAI,CAAC,EAAE,MAAM,CAAC;CACf;AAED,yBAAiB,kBAAkB,CAAC;IAClC,UAAiB,IAAI;QACnB,KAAK,EAAE,MAAM,CAAC;QAEd,MAAM,EAAE,MAAM,CAAC;QAEf,OAAO,EAAE,MAAM,CAAC;QAEhB,GAAG,EAAE,MAAM,CAAC;KACb;IAED,UAAiB,GAAG;QAClB,KAAK,EAAE,MAAM,CAAC;QAEd,OAAO,EAAE,MAAM,CAAC;QAEhB,GAAG,EAAE,MAAM,CAAC;KACb;CACF;AAED,MAAM,WAAW,kBAAkB;IACjC,OAAO,CAAC,EAAE,OAAO,CAAC;IAElB,IAAI,CAAC,EAAE,kBAAkB,CAAC,IAAI,CAAC;IAE/B,GAAG,CAAC,EAAE,kBAAkB,CAAC,GAAG,CAAC;CAC9B;AAED,yBAAiB,kBAAkB,CAAC;IAClC,UAAiB,IAAI;QACnB,KAAK,CAAC,EAAE,MAAM,CAAC;QAEf,MAAM,CAAC,EAAE,MAAM,CAAC;QAEhB,OAAO,CAAC,EAAE,MAAM,CAAC;QAEjB,GAAG,CAAC,EAAE,MAAM,CAAC;KACd;IAED,UAAiB,GAAG;QAClB,KAAK,CAAC,EAAE,MAAM,CAAC;QAEf,OAAO,CAAC,EAAE,MAAM,CAAC;QAEjB,GAAG,CAAC,EAAE,MAAM,CAAC;KACd;CACF;AAED,MAAM,WAAW,gBAAiB,SAAQ,gBAAgB;CAAG;AAE7D,MAAM,WAAW,iBAAkB,SAAQ,gBAAgB;CAAG;AAI9D,MAAM,CAAC,OAAO,WAAW,OAAO,CAAC;IAC/B,OAAO,EACL,KAAK,mBAAmB,IAAI,mBAAmB,EAC/C,KAAK,mBAAmB,IAAI,mBAAmB,EAC/C,KAAK,oBAAoB,IAAI,oBAAoB,EACjD,KAAK,cAAc,IAAI,cAAc,EACrC,yBAAyB,IAAI,yBAAyB,EACtD,KAAK,kBAAkB,IAAI,kBAAkB,EAC7C,KAAK,kBAAkB,IAAI,kBAAkB,EAC7C,KAAK,gBAAgB,IAAI,gBAAgB,EACzC,KAAK,iBAAiB,IAAI,iBAAiB,GAC5C,CAAC;CACH;AAED,OAAO,EAAE,yBAAyB,EAAE,CAAC"}