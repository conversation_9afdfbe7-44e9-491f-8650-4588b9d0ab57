"use strict";
// File generated from our OpenAPI spec by Stainless. See CONTRIBUTING.md for details.
Object.defineProperty(exports, "__esModule", { value: true });
exports.UserConnectionResponsesOffsetPage = exports.UserConnections = void 0;
const resource_1 = require("../../resource.js");
const core_1 = require("../../core.js");
const pagination_1 = require("../../pagination.js");
class UserConnections extends resource_1.APIResource {
    list(query = {}, options) {
        if ((0, core_1.isRequestOptions)(query)) {
            return this.list({}, query);
        }
        return this._client.getAPIList('/v1/admin/user_connections', UserConnectionResponsesOffsetPage, {
            query,
            ...options,
        });
    }
    /**
     * Delete a user/auth provider connection
     */
    delete(id, options) {
        return this._client.delete(`/v1/admin/user_connections/${id}`, {
            ...options,
            headers: { Accept: '*/*', ...options?.headers },
        });
    }
}
exports.UserConnections = UserConnections;
class UserConnectionResponsesOffsetPage extends pagination_1.OffsetPage {
}
exports.UserConnectionResponsesOffsetPage = UserConnectionResponsesOffsetPage;
UserConnections.UserConnectionResponsesOffsetPage = UserConnectionResponsesOffsetPage;
//# sourceMappingURL=user-connections.js.map