import os
import requests
import time
from typing import Dict, Any, Optional

class DropcontactProvider:
    def __init__(self):
        self.base_url = "https://api.dropcontact.com/v1"
        self.api_key = os.getenv("DROPCONTACT_API_KEY")
        
        if not self.api_key:
            raise ValueError("DROPCONTACT_API_KEY environment variable is required")
        
        self.endpoints = {
            "enrich_contact": {
                "route": "/enrich/all",
                "method": "POST",
                "name": "Enrichir Contact",
                "description": "Enrichit les données d'un contact (email, téléphone, entreprise, SIREN, etc.)",
                "payload": {
                    "first_name": "Prénom de la personne",
                    "last_name": "Nom de famille de la personne", 
                    "company": "Nom de l'entreprise",
                    "email": "Email à vérifier (optionnel)",
                    "website": "Site web de l'entreprise (optionnel)",
                    "phone": "Numéro de téléphone (optionnel)",
                    "job": "Poste/fonction (optionnel)",
                    "country": "Code pays (ex: 'FR' pour France)",
                    "linkedin": "URL LinkedIn du contact (optionnel)"
                }
            },
            "verify_email": {
                "route": "/enrich/all", 
                "method": "POST",
                "name": "Vérifier Email",
                "description": "Vérifie et qualifie un email existant",
                "payload": {
                    "email": "Email à vérifier"
                }
            },
            "find_email": {
                "route": "/enrich/all",
                "method": "POST", 
                "name": "Trouver Email",
                "description": "Trouve l'email professionnel d'une personne à partir de son nom et entreprise",
                "payload": {
                    "first_name": "Prénom de la personne",
                    "last_name": "Nom de famille de la personne",
                    "company": "Nom de l'entreprise"
                }
            },
            "enrich_company": {
                "route": "/enrich/all",
                "method": "POST",
                "name": "Enrichir Entreprise", 
                "description": "Enrichit les données d'une entreprise (SIREN, SIRET, adresse, effectifs, CA, etc.)",
                "payload": {
                    "company": "Nom de l'entreprise",
                    "website": "Site web de l'entreprise (optionnel)",
                    "num_siren": "Numéro SIREN (optionnel)"
                }
            }
        }
    
    def get_endpoints(self):
        """Retourne la liste des endpoints disponibles"""
        return self.endpoints
    
    def call_endpoint(self, route: str, payload: Optional[Dict[str, Any]] = None):
        """
        Appelle un endpoint de l'API Dropcontact
        
        Args:
            route: Route de l'endpoint (ex: "enrich_contact")
            payload: Données à envoyer
            
        Returns:
            dict: Réponse de l'API avec les données enrichies
        """
        if route.startswith("/"):
            route = route[1:]
            
        endpoint = self.endpoints.get(route)
        if not endpoint:
            raise ValueError(f"Endpoint {route} not found")
        
        # Préparer les données selon le format Dropcontact
        data_payload = {
            "data": [payload] if payload else [{}],
            "siren": True,  # Inclure les données SIREN/SIRET
            "language": "fr"  # Résultats en français
        }
        
        headers = {
            "Content-Type": "application/json",
            "X-Access-Token": self.api_key
        }
        
        url = f"{self.base_url}{endpoint['route']}"
        
        try:
            # Étape 1: POST pour lancer le traitement
            response = requests.post(url, json=data_payload, headers=headers)
            response.raise_for_status()
            
            post_result = response.json()
            
            if not post_result.get("success"):
                return {
                    "error": True,
                    "message": post_result.get("reason", "Erreur lors du lancement du traitement")
                }
            
            request_id = post_result.get("request_id")
            credits_left = post_result.get("credits_left")
            
            # Étape 2: GET pour récupérer les résultats
            get_url = f"{url}/{request_id}"
            max_attempts = 10
            wait_time = 3
            
            for attempt in range(max_attempts):
                time.sleep(wait_time)
                
                get_response = requests.get(get_url, headers=headers)
                get_response.raise_for_status()
                
                result = get_response.json()
                
                if result.get("success"):
                    # Traitement réussi
                    enriched_data = result.get("data", [])
                    return {
                        "success": True,
                        "data": enriched_data,
                        "credits_left": credits_left,
                        "request_id": request_id
                    }
                elif result.get("error"):
                    return {
                        "error": True,
                        "message": result.get("reason", "Erreur lors du traitement")
                    }
                # Sinon, continuer à attendre
                
            # Timeout - forcer les résultats partiels
            force_url = f"{get_url}?forceResults=true"
            force_response = requests.get(force_url, headers=headers)
            force_response.raise_for_status()
            
            force_result = force_response.json()
            return {
                "success": True,
                "data": force_result.get("data", []),
                "credits_left": credits_left,
                "request_id": request_id,
                "partial": True,
                "message": "Résultats partiels (timeout)"
            }
            
        except requests.exceptions.RequestException as e:
            return {
                "error": True,
                "message": f"Erreur API Dropcontact: {str(e)}"
            }
        except Exception as e:
            return {
                "error": True, 
                "message": f"Erreur inattendue: {str(e)}"
            }
    
    def enrich_contact(self, first_name: str, last_name: str, company: str, **kwargs):
        """
        Méthode helper pour enrichir un contact
        """
        payload = {
            "first_name": first_name,
            "last_name": last_name, 
            "company": company,
            **kwargs
        }
        return self.call_endpoint("enrich_contact", payload)
    
    def verify_email(self, email: str):
        """
        Méthode helper pour vérifier un email
        """
        payload = {"email": email}
        return self.call_endpoint("verify_email", payload)
    
    def find_email(self, first_name: str, last_name: str, company: str):
        """
        Méthode helper pour trouver un email
        """
        payload = {
            "first_name": first_name,
            "last_name": last_name,
            "company": company
        }
        return self.call_endpoint("find_email", payload)
    
    def enrich_company(self, company: str, **kwargs):
        """
        Méthode helper pour enrichir une entreprise
        """
        payload = {
            "company": company,
            **kwargs
        }
        return self.call_endpoint("enrich_company", payload)


if __name__ == "__main__":
    from dotenv import load_dotenv
    load_dotenv()
    
    provider = DropcontactProvider()
    
    # Test d'enrichissement de contact
    print("Test enrichissement contact...")
    result = provider.enrich_contact(
        first_name="Jean",
        last_name="Dupont", 
        company="Dropcontact"
    )
    print("Résultat:", result)
    
    # Test de vérification d'email
    print("\nTest vérification email...")
    result2 = provider.verify_email("<EMAIL>")
    print("Résultat:", result2)
