import asyncio
from typing import Dict, Any, List, Optional
from datetime import datetime, timedelta

from ..domain.entities import Connection, AuthFlow, AuthStatus, ConnectionStatus
from ..domain.value_objects import UserId
from ..domain.exceptions import AuthenticationException, AuthorizationException
from ..support.arcade_client import ArcadeClient
from ..protocols import Logger


class AuthService:
    """
    Service d'authentification Arcade.dev
    Gère les flux OAuth et les connexions utilisateur
    """
    
    def __init__(self, arcade_client: ArcadeClient, logger: Logger):
        self._arcade_client = arcade_client
        self._logger = logger
        self._active_auth_flows: Dict[str, AuthFlow] = {}
    
    async def create_auth_flow(
        self, 
        user_id: UserId, 
        toolkit_name: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        Crée un flux d'authentification pour un utilisateur
        """
        try:
            if not toolkit_name:
                raise AuthenticationException("Toolkit name is required for authentication")
            
            self._logger.info(f"Creating auth flow for user {user_id.value} and toolkit {toolkit_name}")
            
            # <PERSON><PERSON>marrer le flux d'authentification avec Arcade
            auth_response = await self._arcade_client.start_auth_flow(user_id.value, toolkit_name)
            
            if not auth_response.get("success"):
                raise AuthenticationException(f"Failed to start auth flow: {auth_response.get('error')}")
            
            # Créer l'objet AuthFlow
            auth_flow = AuthFlow(
                auth_id=auth_response["auth_id"],
                user_id=user_id,
                toolkit_name=toolkit_name,
                provider=auth_response.get("provider", "oauth2"),
                auth_url=auth_response["url"],
                status=AuthStatus.PENDING
            )
            
            # Stocker le flux actif
            self._active_auth_flows[auth_flow.auth_id] = auth_flow
            
            self._logger.info(f"Auth flow created with ID: {auth_flow.auth_id}")
            
            return {
                "success": True,
                "auth_id": auth_flow.auth_id,
                "url": auth_flow.auth_url,
                "status": auth_flow.status.value,
                "user_id": user_id.value,
                "toolkit": toolkit_name,
                "provider": auth_flow.provider
            }
            
        except Exception as e:
            self._logger.error(f"Error creating auth flow: {str(e)}")
            return {
                "success": False,
                "error": str(e),
                "user_id": user_id.value,
                "toolkit": toolkit_name
            }
    
    async def check_auth_status(self, auth_id: str) -> Dict[str, Any]:
        """
        Vérifie le statut d'un flux d'authentification
        """
        try:
            if auth_id not in self._active_auth_flows:
                raise AuthenticationException(f"Auth flow {auth_id} not found")
            
            auth_flow = self._active_auth_flows[auth_id]
            
            # Vérifier le statut avec Arcade
            status_response = await self._arcade_client.check_auth_status(auth_id)
            
            # Mettre à jour le statut local
            if status_response.get("status") == "completed":
                auth_flow.complete()
            elif status_response.get("status") == "failed":
                auth_flow.fail(status_response.get("error", "Authentication failed"))
            
            return {
                "auth_id": auth_id,
                "status": auth_flow.status.value,
                "completed_at": auth_flow.completed_at.isoformat() if auth_flow.completed_at else None,
                "error": auth_flow.error_message
            }
            
        except Exception as e:
            self._logger.error(f"Error checking auth status: {str(e)}")
            return {
                "auth_id": auth_id,
                "status": "error",
                "error": str(e)
            }
    
    async def get_user_connections(self, user_id: UserId) -> List[Connection]:
        """
        Récupère les connexions actives d'un utilisateur
        """
        try:
            self._logger.info(f"Getting connections for user {user_id.value}")
            
            # Pour l'instant, on simule les connexions
            # Dans une implémentation complète, on interrogerait Arcade ou la DB
            connections = []
            
            # TODO: Implémenter la récupération réelle des connexions
            # depuis Arcade.dev ou depuis la base de données locale
            
            return connections
            
        except Exception as e:
            self._logger.error(f"Error getting user connections: {str(e)}")
            return []
    
    async def revoke_connection(self, user_id: UserId, toolkit_name: str) -> bool:
        """
        Révoque une connexion utilisateur
        """
        try:
            self._logger.info(f"Revoking connection for user {user_id.value} and toolkit {toolkit_name}")
            
            # TODO: Implémenter la révocation via Arcade.dev
            # Pour l'instant, on retourne True
            
            return True
            
        except Exception as e:
            self._logger.error(f"Error revoking connection: {str(e)}")
            return False
    
    async def refresh_token(self, user_id: UserId, toolkit_name: str) -> Optional[Connection]:
        """
        Rafraîchit un token d'accès
        """
        try:
            self._logger.info(f"Refreshing token for user {user_id.value} and toolkit {toolkit_name}")
            
            # TODO: Implémenter le rafraîchissement de token via Arcade.dev
            
            return None
            
        except Exception as e:
            self._logger.error(f"Error refreshing token: {str(e)}")
            return None
    
    def cleanup_expired_auth_flows(self) -> None:
        """
        Nettoie les flux d'authentification expirés
        """
        try:
            current_time = datetime.utcnow()
            expired_flows = []
            
            for auth_id, auth_flow in self._active_auth_flows.items():
                # Considérer comme expiré après 1 heure
                if (current_time - auth_flow.created_at) > timedelta(hours=1):
                    expired_flows.append(auth_id)
            
            for auth_id in expired_flows:
                del self._active_auth_flows[auth_id]
                self._logger.info(f"Cleaned up expired auth flow: {auth_id}")
                
        except Exception as e:
            self._logger.error(f"Error cleaning up auth flows: {str(e)}")
    
    async def close(self):
        """Ferme le service d'authentification"""
        self._active_auth_flows.clear()
