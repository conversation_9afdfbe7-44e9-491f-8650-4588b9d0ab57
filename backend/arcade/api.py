from fastapi import APIRouter, HTTPException, Depends
from typing import List, Dict, Any, Optional
from pydantic import BaseModel
from uuid import UUID

from utils.logger import logger
from utils.auth_utils import get_current_user_id_from_jwt
from .facade import ArcadeManager
from .domain.entities import Profile
from .domain.exceptions import (
    ProfileNotFoundError, ProfileAlreadyExistsError,
    ArcadeException, AuthenticationException
)

router = APIRouter(prefix="/arcade", tags=["arcade"])
arcade_manager: Optional[ArcadeManager] = None


def initialize(database):
    global arcade_manager
    if arcade_manager is None:
        arcade_manager = ArcadeManager(db=database, logger=logger)


# Modèles Pydantic (compatibles avec Pipedream)
class CreateConnectionTokenRequest(BaseModel):
    toolkit: Optional[str] = None


class ConnectionTokenResponse(BaseModel):
    success: bool
    auth_url: Optional[str] = None
    auth_id: Optional[str] = None
    user_id: str = ""
    toolkit: Optional[str] = None
    status: Optional[str] = None
    error: Optional[str] = None


class ConnectionResponse(BaseModel):
    success: bool
    connections: List[Dict[str, Any]]
    count: int
    error: Optional[str] = None


class ToolDiscoveryRequest(BaseModel):
    toolkit_name: Optional[str] = None
    oauth_app_id: Optional[str] = None


class ToolDiscoveryResponse(BaseModel):
    success: bool
    tools: List[Dict[str, Any]]
    count: int
    error: Optional[str] = None


class ToolConnectionRequest(BaseModel):
    toolkit_name: str
    oauth_app_id: Optional[str] = None


class ToolConnectionResponse(BaseModel):
    success: bool
    toolkit_config: Optional[Dict[str, Any]] = None
    error: Optional[str] = None


class ProfileRequest(BaseModel):
    profile_name: str
    toolkit_name: str
    app_name: str
    description: Optional[str] = None
    is_default: bool = False
    oauth_app_id: Optional[str] = None
    enabled_tools: List[str] = []
    external_user_id: Optional[str] = None


class UpdateProfileRequest(BaseModel):
    profile_name: Optional[str] = None
    display_name: Optional[str] = None
    is_active: Optional[bool] = None
    is_default: Optional[bool] = None
    enabled_tools: Optional[List[str]] = None


# Endpoints compatibles avec l'API Pipedream

@router.post("/connection-token", response_model=ConnectionTokenResponse)
async def create_connection_token(
    request: CreateConnectionTokenRequest,
    user_id: str = Depends(get_current_user_id_from_jwt)
):
    """Crée un token de connexion Arcade (compatible Pipedream)"""
    if not arcade_manager:
        raise HTTPException(status_code=500, detail="Arcade manager not initialized")
    
    try:
        result = await arcade_manager.create_connection_token(user_id, request.toolkit)
        
        return ConnectionTokenResponse(
            success=result["success"],
            auth_url=result.get("url"),
            auth_id=result.get("auth_id"),
            user_id=user_id,
            toolkit=request.toolkit,
            status=result.get("status"),
            error=result.get("error")
        )
        
    except Exception as e:
        logger.error(f"Error creating connection token: {str(e)}")
        return ConnectionTokenResponse(
            success=False,
            user_id=user_id,
            error=str(e)
        )


@router.get("/connections", response_model=ConnectionResponse)
async def get_connections(user_id: str = Depends(get_current_user_id_from_jwt)):
    """Récupère les connexions utilisateur (compatible Pipedream)"""
    if not arcade_manager:
        raise HTTPException(status_code=500, detail="Arcade manager not initialized")
    
    try:
        connections = await arcade_manager.get_connections(user_id)
        
        connection_data = []
        for conn in connections:
            connection_data.append({
                "toolkit": conn.toolkit_name,
                "app_name": conn.app_name,
                "is_active": conn.is_active,
                "created_at": conn.created_at.isoformat(),
                "updated_at": conn.updated_at.isoformat()
            })
        
        return ConnectionResponse(
            success=True,
            connections=connection_data,
            count=len(connection_data)
        )
        
    except Exception as e:
        logger.error(f"Error getting connections: {str(e)}")
        return ConnectionResponse(
            success=False,
            connections=[],
            count=0,
            error=str(e)
        )


@router.post("/tools/discover", response_model=ToolDiscoveryResponse)
async def discover_tools(
    request: ToolDiscoveryRequest,
    user_id: str = Depends(get_current_user_id_from_jwt)
):
    """Découvre les outils disponibles (compatible avec /mcp/discover)"""
    if not arcade_manager:
        raise HTTPException(status_code=500, detail="Arcade manager not initialized")
    
    try:
        tools = await arcade_manager.discover_available_tools(user_id, request.toolkit_name)
        
        tool_data = []
        for tool in tools:
            tool_data.append({
                "name": tool.name,
                "description": tool.description,
                "input_schema": tool.input_schema,
                "toolkit": tool.toolkit_name,
                "requires_auth": tool.requires_auth
            })
        
        return ToolDiscoveryResponse(
            success=True,
            tools=tool_data,
            count=len(tool_data)
        )
        
    except Exception as e:
        logger.error(f"Error discovering tools: {str(e)}")
        return ToolDiscoveryResponse(
            success=False,
            tools=[],
            count=0,
            error=str(e)
        )


@router.post("/tools/connect", response_model=ToolConnectionResponse)
async def connect_toolkit(
    request: ToolConnectionRequest,
    user_id: str = Depends(get_current_user_id_from_jwt)
):
    """Connecte un toolkit (compatible avec /mcp/connect)"""
    if not arcade_manager:
        raise HTTPException(status_code=500, detail="Arcade manager not initialized")
    
    try:
        toolkit_info = await arcade_manager.create_tool_connection(
            user_id, 
            request.toolkit_name, 
            request.oauth_app_id
        )
        
        toolkit_config = {
            "toolkit_name": toolkit_info.name,
            "display_name": toolkit_info.display_name,
            "description": toolkit_info.description,
            "tool_count": len(toolkit_info.available_tools),
            "status": "connected",
            "available_tools": [
                {
                    "name": tool.name,
                    "description": tool.description,
                    "input_schema": tool.input_schema
                }
                for tool in toolkit_info.available_tools
            ]
        }
        
        return ToolConnectionResponse(
            success=True,
            toolkit_config=toolkit_config
        )
        
    except Exception as e:
        logger.error(f"Error connecting toolkit: {str(e)}")
        return ToolConnectionResponse(
            success=False,
            error=str(e)
        )


@router.get("/toolkits")
async def get_toolkits(
    search: Optional[str] = None,
    category: Optional[str] = None,
    page: int = 1,
    limit: int = 20
):
    """Liste les toolkits disponibles (compatible avec /apps)"""
    if not arcade_manager:
        raise HTTPException(status_code=500, detail="Arcade manager not initialized")
    
    try:
        result = await arcade_manager.search_toolkits(search, category, page, limit)
        return result
        
    except Exception as e:
        logger.error(f"Error getting toolkits: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/toolkits/popular")
async def get_popular_toolkits(category: Optional[str] = None, limit: int = 100):
    """Récupère les toolkits populaires (compatible avec /apps/popular)"""
    if not arcade_manager:
        raise HTTPException(status_code=500, detail="Arcade manager not initialized")
    
    try:
        toolkits = await arcade_manager.get_popular_toolkits(category, limit)
        
        toolkit_data = []
        for toolkit in toolkits:
            toolkit_data.append({
                "name": toolkit.name,
                "display_name": toolkit.display_name,
                "description": toolkit.description,
                "category": toolkit.category,
                "tool_count": len(toolkit.available_tools)
            })
        
        return {
            "success": True,
            "toolkits": toolkit_data,
            "count": len(toolkit_data)
        }
        
    except Exception as e:
        logger.error(f"Error getting popular toolkits: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


# Endpoints de gestion des profils (compatibles Pipedream)

@router.post("/profiles", response_model=Dict[str, Any])
async def create_profile(
    request: ProfileRequest,
    user_id: str = Depends(get_current_user_id_from_jwt)
):
    """Crée un profil Arcade"""
    if not arcade_manager:
        raise HTTPException(status_code=500, detail="Arcade manager not initialized")
    
    try:
        profile = await arcade_manager.create_profile(
            user_id,
            request.profile_name,
            request.toolkit_name,
            request.app_name,
            request.description,
            request.is_default,
            request.oauth_app_id,
            request.enabled_tools,
            request.external_user_id
        )
        
        return {
            "success": True,
            "profile": {
                "profile_id": str(profile.profile_id),
                "profile_name": profile.profile_name,
                "toolkit_name": profile.toolkit_name,
                "app_name": profile.app_name,
                "is_active": profile.is_active,
                "is_default": profile.is_default,
                "enabled_tools": profile.enabled_tools
            }
        }
        
    except ProfileAlreadyExistsError as e:
        raise HTTPException(status_code=409, detail=str(e))
    except Exception as e:
        logger.error(f"Error creating profile: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/profiles")
async def get_profiles(
    toolkit_name: Optional[str] = None,
    is_active: Optional[bool] = None,
    user_id: str = Depends(get_current_user_id_from_jwt)
):
    """Liste les profils utilisateur"""
    if not arcade_manager:
        raise HTTPException(status_code=500, detail="Arcade manager not initialized")
    
    try:
        profiles = await arcade_manager.get_profiles(user_id, toolkit_name, is_active)
        
        profile_data = []
        for profile in profiles:
            profile_data.append({
                "profile_id": str(profile.profile_id),
                "profile_name": profile.profile_name,
                "toolkit_name": profile.toolkit_name,
                "app_name": profile.app_name,
                "description": profile.description,
                "is_active": profile.is_active,
                "is_default": profile.is_default,
                "enabled_tools": profile.enabled_tools,
                "created_at": profile.created_at.isoformat(),
                "updated_at": profile.updated_at.isoformat()
            })
        
        return {
            "success": True,
            "profiles": profile_data,
            "count": len(profile_data)
        }
        
    except Exception as e:
        logger.error(f"Error getting profiles: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))
