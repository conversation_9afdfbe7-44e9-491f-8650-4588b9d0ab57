"use strict";
// File generated from our OpenAPI spec by Stainless. See CONTRIBUTING.md for details.
Object.defineProperty(exports, "__esModule", { value: true });
exports.AuthProviders = void 0;
const resource_1 = require("../../resource.js");
class AuthProviders extends resource_1.APIResource {
    /**
     * Create a new auth provider
     */
    create(body, options) {
        return this._client.post('/v1/admin/auth_providers', { body, ...options });
    }
    /**
     * List a page of auth providers that are available to the caller
     */
    list(options) {
        return this._client.get('/v1/admin/auth_providers', options);
    }
    /**
     * Delete a specific auth provider
     */
    delete(id, options) {
        return this._client.delete(`/v1/admin/auth_providers/${id}`, options);
    }
    /**
     * Get the details of a specific auth provider
     */
    get(id, options) {
        return this._client.get(`/v1/admin/auth_providers/${id}`, options);
    }
    /**
     * Patch an existing auth provider
     */
    patch(pathId, body, options) {
        return this._client.patch(`/v1/admin/auth_providers/${pathId}`, { body, ...options });
    }
}
exports.AuthProviders = AuthProviders;
//# sourceMappingURL=auth-providers.js.map