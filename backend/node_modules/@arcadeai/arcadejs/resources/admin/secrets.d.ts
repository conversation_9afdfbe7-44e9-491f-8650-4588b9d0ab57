import { APIResource } from "../../resource.js";
import * as Core from "../../core.js";
export declare class Secrets extends APIResource {
    /**
     * List all secrets that are visible to the caller
     */
    list(options?: Core.RequestOptions): Core.APIPromise<SecretListResponse>;
    /**
     * Delete a secret by its ID
     */
    delete(secretId: string, options?: Core.RequestOptions): Core.APIPromise<void>;
}
export interface SecretResponse {
    id?: string;
    binding?: SecretResponse.Binding;
    created_at?: string;
    description?: string;
    hint?: string;
    key?: string;
    last_accessed_at?: string;
    updated_at?: string;
}
export declare namespace SecretResponse {
    interface Binding {
        id?: string;
        type?: 'static' | 'tenant' | 'project' | 'account';
    }
}
export interface SecretListResponse {
    items?: Array<SecretResponse>;
    limit?: number;
    offset?: number;
    page_count?: number;
    total_count?: number;
}
export declare namespace Secrets {
    export { type SecretResponse as SecretResponse, type SecretListResponse as SecretListResponse };
}
//# sourceMappingURL=secrets.d.ts.map