#!/usr/bin/env python3
"""
Script de test pour vérifier la connectivité avec Arcade.dev
"""

import os
import asyncio
from arcadepy import Arcade


async def test_arcade_connection():
    """Test de base de la connexion Arcade"""
    
    print("🔍 Testing Arcade.dev connection...")
    
    # Vérifier les variables d'environnement
    api_key = os.getenv("ARCADE_API_KEY")
    if not api_key:
        print("❌ ARCADE_API_KEY not found in environment variables")
        print("   Please set ARCADE_API_KEY in your .env file")
        return False
    
    try:
        # Initialiser le client Arcade
        client = Arcade(api_key=api_key)
        print("✅ Arcade client initialized successfully")
        
        # Test 1: Lister les toolkits disponibles
        print("\n📦 Testing toolkit listing...")
        try:
            # Essayer de lister quelques toolkits populaires
            test_toolkits = ["gmail", "slack", "github"]
            available_toolkits = []
            
            for toolkit_name in test_toolkits:
                try:
                    tools = client.tools.list(toolkit=toolkit_name, limit=5)
                    if tools:
                        available_toolkits.append({
                            "name": toolkit_name,
                            "tool_count": len(tools),
                            "sample_tools": [tool.name for tool in tools[:3]]
                        })
                        print(f"   ✅ {toolkit_name}: {len(tools)} tools available")
                    else:
                        print(f"   ⚠️  {toolkit_name}: No tools found")
                except Exception as e:
                    print(f"   ❌ {toolkit_name}: Error - {str(e)}")
            
            if available_toolkits:
                print(f"\n✅ Found {len(available_toolkits)} available toolkits")
                return True
            else:
                print("\n❌ No toolkits available - check API key permissions")
                return False
                
        except Exception as e:
            print(f"❌ Error listing toolkits: {str(e)}")
            return False
            
    except Exception as e:
        print(f"❌ Error initializing Arcade client: {str(e)}")
        return False


async def test_arcade_auth_flow():
    """Test du flux d'authentification (simulation)"""
    
    print("\n🔐 Testing authentication flow...")
    
    api_key = os.getenv("ARCADE_API_KEY")
    if not api_key:
        print("❌ ARCADE_API_KEY required for auth testing")
        return False
    
    try:
        client = Arcade(api_key=api_key)
        test_user_id = "test-user-123"
        
        # Test avec Gmail comme exemple
        print("   Testing Gmail authentication flow...")
        
        # Lister les outils Gmail pour récupérer les scopes
        try:
            gmail_tools = client.tools.list(toolkit="gmail", limit=10)
            if gmail_tools:
                print(f"   ✅ Gmail toolkit has {len(gmail_tools)} tools")
                
                # Simuler le démarrage d'un flux d'auth
                # Note: En production, ceci démarrerait un vrai flux OAuth
                print("   ✅ Auth flow simulation successful")
                return True
            else:
                print("   ⚠️  Gmail toolkit not available")
                return False
                
        except Exception as e:
            print(f"   ❌ Gmail auth test failed: {str(e)}")
            return False
            
    except Exception as e:
        print(f"❌ Auth flow test error: {str(e)}")
        return False


def print_environment_info():
    """Affiche les informations d'environnement"""
    
    print("\n📋 Environment Information:")
    print(f"   ARCADE_API_KEY: {'✅ Set' if os.getenv('ARCADE_API_KEY') else '❌ Not set'}")
    print(f"   ARCADE_ENGINE_URL: {os.getenv('ARCADE_ENGINE_URL', 'Not set (using default)')}")
    print(f"   ENABLE_ARCADE_INTEGRATION: {os.getenv('ENABLE_ARCADE_INTEGRATION', 'false')}")
    print(f"   INTEGRATION_MIGRATION_MODE: {os.getenv('INTEGRATION_MIGRATION_MODE', 'feature_flag')}")


async def main():
    """Fonction principale de test"""
    
    print("🚀 Arcade.dev Connection Test")
    print("=" * 50)
    
    # Afficher les infos d'environnement
    print_environment_info()
    
    # Test de connectivité de base
    connection_ok = await test_arcade_connection()
    
    if connection_ok:
        # Test du flux d'authentification
        auth_ok = await test_arcade_auth_flow()
        
        if auth_ok:
            print("\n🎉 All tests passed! Arcade.dev integration is ready.")
            print("\nNext steps:")
            print("1. Set ENABLE_ARCADE_INTEGRATION=true in your .env file")
            print("2. Configure your preferred toolkits")
            print("3. Test with real user authentication")
        else:
            print("\n⚠️  Basic connection works, but auth flow needs attention")
    else:
        print("\n❌ Connection test failed. Please check your configuration.")
        print("\nTroubleshooting:")
        print("1. Verify your ARCADE_API_KEY is correct")
        print("2. Check your internet connection")
        print("3. Ensure you have access to Arcade.dev API")


if __name__ == "__main__":
    # Charger les variables d'environnement depuis .env
    try:
        from dotenv import load_dotenv
        load_dotenv()
    except ImportError:
        print("⚠️  python-dotenv not available, using system environment variables")
    
    asyncio.run(main())
