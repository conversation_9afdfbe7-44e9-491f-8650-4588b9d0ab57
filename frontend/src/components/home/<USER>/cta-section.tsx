import Image from 'next/image';
import { siteConfig } from '@/lib/home';
import Link from 'next/link';
import { HeroVideoSection } from './hero-video-section';

export function CTASection() {
  const { ctaSection } = siteConfig;

  return (
    <section
      id="cta"
      className="flex flex-col items-center justify-center w-full pt-12 pb-12"
    >
      <div className="w-full max-w-6xl mx-auto px-6">
        <div className="h-[400px] md:h-[400px] overflow-hidden w-full border border-border/30 rounded-xl bg-[#F9FAFB]/[0.07] backdrop-blur-md relative z-20">
          <div className="absolute inset-0 flex flex-col items-center justify-center p-8">
            <h1 className="text-primary dark:text-white text-4xl md:text-5xl font-medium tracking-tighter max-w-xs md:max-w-xl text-center mb-8">
              {ctaSection.title}
            </h1>
            <div className="flex flex-col items-center justify-center gap-4">
              <Link
                href={ctaSection.button.href}
                className="bg-gradient-to-r from-green-500 to-emerald-600 text-white font-medium text-sm h-12 w-fit px-8 rounded-full flex items-center justify-center shadow-[0_4px_12px_rgba(0,0,0,0.15)] hover:shadow-[0_6px_16px_rgba(0,0,0,0.2)] transition-all duration-300 ease-in-out"
              >
                {ctaSection.button.text}
              </Link>
              <span className="text-muted-foreground dark:text-gray-300 text-sm mt-2">
                {ctaSection.subtext}
              </span>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}
