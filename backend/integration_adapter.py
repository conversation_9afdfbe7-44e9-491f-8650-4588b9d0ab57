"""
Adaptateur de compatibilité pour la migration Pipedream → Arcade.dev
Permet une transition progressive sans casser l'existant
"""

import os
from typing import Dict, Any, List, Optional, Union
from utils.logger import logger


class IntegrationAdapter:
    """
    Adaptateur qui route les appels vers Pipedream ou Arcade
    selon la configuration ou les préférences utilisateur
    """
    
    def __init__(self):
        self.arcade_enabled = os.getenv("ENABLE_ARCADE_INTEGRATION", "false").lower() == "true"
        self.migration_mode = os.getenv("INTEGRATION_MIGRATION_MODE", "feature_flag")  # ou "user_choice"
        
        # Lazy loading des managers
        self._pipedream_manager = None
        self._arcade_manager = None
    
    @property
    def pipedream_manager(self):
        if self._pipedream_manager is None:
            from pipedream.facade import PipedreamManager
            from services.supabase import DBConnection
            self._pipedream_manager = PipedreamManager(db=DBConnection(), logger=logger)
        return self._pipedream_manager
    
    @property
    def arcade_manager(self):
        if self._arcade_manager is None:
            from arcade.facade import ArcadeManager
            from services.supabase import DBConnection
            self._arcade_manager = ArcadeManager(db=DBConnection(), logger=logger)
        return self._arcade_manager
    
    def should_use_arcade(self, user_id: Optional[str] = None) -> bool:
        """
        Détermine si on doit utiliser Arcade pour cet utilisateur
        """
        if self.migration_mode == "feature_flag":
            return self.arcade_enabled
        
        elif self.migration_mode == "user_choice":
            if user_id:
                return self._get_user_integration_preference(user_id) == "arcade"
            return False
        
        return False
    
    def _get_user_integration_preference(self, user_id: str) -> str:
        """
        Récupère la préférence d'intégration de l'utilisateur
        """
        # TODO: Implémenter la logique de récupération depuis la DB
        # Pour l'instant, retourne Pipedream par défaut
        return "pipedream"
    
    async def create_profile(
        self,
        account_id: str,
        profile_name: str,
        app_slug_or_toolkit: str,
        app_name: str,
        description: Optional[str] = None,
        is_default: bool = False,
        oauth_app_id: Optional[str] = None,
        enabled_tools: Optional[List[str]] = None,
        external_user_id: Optional[str] = None
    ):
        """
        Crée un profil via Pipedream ou Arcade selon la configuration
        """
        if self.should_use_arcade(account_id):
            logger.info(f"Creating Arcade profile for user {account_id}")
            return await self.arcade_manager.create_profile(
                account_id=account_id,
                profile_name=profile_name,
                toolkit_name=app_slug_or_toolkit,  # Arcade utilise toolkit_name
                app_name=app_name,
                description=description,
                is_default=is_default,
                oauth_app_id=oauth_app_id,
                enabled_tools=enabled_tools,
                external_user_id=external_user_id
            )
        else:
            logger.info(f"Creating Pipedream profile for user {account_id}")
            return await self.pipedream_manager.create_profile(
                account_id=account_id,
                profile_name=profile_name,
                app_slug=app_slug_or_toolkit,  # Pipedream utilise app_slug
                app_name=app_name,
                description=description,
                is_default=is_default,
                oauth_app_id=oauth_app_id,
                enabled_tools=enabled_tools,
                external_user_id=external_user_id
            )
    
    async def get_profiles(
        self,
        account_id: str,
        app_slug_or_toolkit: Optional[str] = None,
        is_active: Optional[bool] = None
    ):
        """
        Récupère les profils via Pipedream ou Arcade
        """
        if self.should_use_arcade(account_id):
            return await self.arcade_manager.get_profiles(
                account_id, app_slug_or_toolkit, is_active
            )
        else:
            return await self.pipedream_manager.get_profiles(
                account_id, app_slug_or_toolkit, is_active
            )
    
    async def create_connection_token(
        self,
        external_user_id: str,
        app_or_toolkit: Optional[str] = None
    ):
        """
        Crée un token de connexion via Pipedream ou Arcade
        """
        if self.should_use_arcade(external_user_id):
            return await self.arcade_manager.create_connection_token(
                external_user_id, app_or_toolkit
            )
        else:
            return await self.pipedream_manager.create_connection_token(
                external_user_id, app_or_toolkit
            )
    
    async def get_connections(self, external_user_id: str):
        """
        Récupère les connexions via Pipedream ou Arcade
        """
        if self.should_use_arcade(external_user_id):
            return await self.arcade_manager.get_connections(external_user_id)
        else:
            return await self.pipedream_manager.get_connections(external_user_id)
    
    async def discover_tools_or_servers(
        self,
        external_user_id: str,
        app_slug_or_toolkit: Optional[str] = None
    ):
        """
        Découvre les outils/serveurs MCP via Pipedream ou Arcade
        """
        if self.should_use_arcade(external_user_id):
            return await self.arcade_manager.discover_available_tools(
                external_user_id, app_slug_or_toolkit
            )
        else:
            return await self.pipedream_manager.discover_mcp_servers(
                external_user_id, app_slug_or_toolkit
            )
    
    async def search_apps_or_toolkits(
        self,
        query: Optional[str] = None,
        category: Optional[str] = None,
        page: int = 1,
        limit: int = 20,
        cursor: Optional[str] = None,
        user_id: Optional[str] = None
    ):
        """
        Recherche dans les apps/toolkits via Pipedream ou Arcade
        """
        if self.should_use_arcade(user_id):
            return await self.arcade_manager.search_toolkits(
                query, category, page, limit
            )
        else:
            return await self.pipedream_manager.search_apps(
                query, category, page, limit, cursor
            )
    
    async def get_popular_apps_or_toolkits(
        self,
        category: Optional[str] = None,
        limit: int = 100,
        user_id: Optional[str] = None
    ):
        """
        Récupère les apps/toolkits populaires via Pipedream ou Arcade
        """
        if self.should_use_arcade(user_id):
            return await self.arcade_manager.get_popular_toolkits(category, limit)
        else:
            return await self.pipedream_manager.get_popular_apps(category, limit)
    
    def get_mcp_config_for_agent(
        self,
        profile_id: str,
        app_name: str,
        app_slug_or_toolkit: str,
        enabled_tools: List[str],
        user_id: str
    ) -> Dict[str, Any]:
        """
        Génère une configuration MCP compatible selon le provider
        """
        if self.should_use_arcade(user_id):
            # Configuration MCP Arcade
            return {
                "name": app_name,
                "type": "arcade",
                "config": {
                    "url": "https://api.arcade.dev/mcp",
                    "toolkit_name": app_slug_or_toolkit,
                    "user_id": user_id,
                    "profile_id": profile_id,
                    "headers": {
                        "X-Arcade-Toolkit": app_slug_or_toolkit,
                        "X-Arcade-User-ID": user_id,
                        "X-Arcade-Profile-ID": profile_id
                    }
                },
                "enabledTools": enabled_tools,
                "enabled_tools": enabled_tools
            }
        else:
            # Configuration MCP Pipedream (existante)
            return {
                "name": app_name,
                "type": "pipedream",
                "config": {
                    "url": "https://remote.mcp.pipedream.net",
                    "headers": {
                        "x-pd-app-slug": app_slug_or_toolkit
                    },
                    "profile_id": profile_id
                },
                "enabledTools": enabled_tools,
                "enabled_tools": enabled_tools
            }
    
    async def close(self):
        """
        Ferme les connexions des deux managers
        """
        if self._pipedream_manager:
            await self._pipedream_manager.close()
        if self._arcade_manager:
            await self._arcade_manager.close()


# Instance globale de l'adaptateur
integration_adapter = IntegrationAdapter()
