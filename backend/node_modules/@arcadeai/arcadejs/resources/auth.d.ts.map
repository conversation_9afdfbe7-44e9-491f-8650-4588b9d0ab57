{"version": 3, "file": "auth.d.ts", "sourceRoot": "", "sources": ["../src/resources/auth.ts"], "names": [], "mappings": "AAEA,OAAO,EAAE,WAAW,EAAE,MAAM,aAAa,CAAC;AAC1C,OAAO,KAAK,MAAM,MAAM,UAAU,CAAC;AACnC,OAAO,KAAK,IAAI,MAAM,SAAS,CAAC;AAEhC,eAAO,MAAM,0BAA0B,KAAK,CAAC;AAE7C;;GAEG;AACH,qBAAa,kBAAmB,SAAQ,KAAK;gBAC/B,OAAO,EAAE,MAAM;CAI5B;AAED,qBAAa,IAAK,SAAQ,WAAW;IACnC;;;;;;;;;;;;;OAaG;IACH,KAAK,CACH,MAAM,EAAE,MAAM,EACd,QAAQ,EAAE,MAAM,EAChB,OAAO,GAAE,gBAAqB,GAC7B,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,qBAAqB,CAAC;IAiBhD;;OAEG;IACH,SAAS,CACP,IAAI,EAAE,mBAAmB,EACzB,OAAO,CAAC,EAAE,IAAI,CAAC,cAAc,GAC5B,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,qBAAqB,CAAC;IAIhD;;OAEG;IACH,WAAW,CACT,IAAI,EAAE,qBAAqB,EAC3B,OAAO,CAAC,EAAE,IAAI,CAAC,cAAc,GAC5B,IAAI,CAAC,UAAU,CAAC,mBAAmB,CAAC;IAIvC;;;;OAIG;IACH,MAAM,CACJ,KAAK,EAAE,gBAAgB,EACvB,OAAO,CAAC,EAAE,IAAI,CAAC,cAAc,GAC5B,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,qBAAqB,CAAC;IAIhD;;;;;;;;;;;;;;;;;;OAkBG;IACG,iBAAiB,CACrB,gBAAgB,EAAE,MAAM,CAAC,qBAAqB,GAAG,MAAM,GACtD,OAAO,CAAC,MAAM,CAAC,qBAAqB,CAAC;CAwBzC;AAED,MAAM,WAAW,WAAW;IAC1B,gBAAgB,EAAE,WAAW,CAAC,eAAe,CAAC;IAE9C,OAAO,EAAE,MAAM,CAAC;IAEhB;;;OAGG;IACH,QAAQ,CAAC,EAAE,MAAM,CAAC;CACnB;AAED,yBAAiB,WAAW,CAAC;IAC3B,UAAiB,eAAe;QAC9B;;WAEG;QACH,EAAE,CAAC,EAAE,MAAM,CAAC;QAEZ,MAAM,CAAC,EAAE,eAAe,CAAC,MAAM,CAAC;QAEhC;;WAEG;QACH,WAAW,CAAC,EAAE,MAAM,CAAC;QAErB,aAAa,CAAC,EAAE,MAAM,CAAC;KACxB;IAED,UAAiB,eAAe,CAAC;QAC/B,UAAiB,MAAM;YACrB,MAAM,CAAC,EAAE,KAAK,CAAC,MAAM,CAAC,CAAC;SACxB;KACF;CACF;AAED,MAAM,WAAW,kBAAkB;IACjC,OAAO,EAAE,MAAM,CAAC;IAEhB,OAAO,EAAE,MAAM,CAAC;CACjB;AAED,MAAM,WAAW,mBAAmB;IAClC,OAAO,EAAE,MAAM,CAAC;IAEhB,QAAQ,CAAC,EAAE,MAAM,CAAC;CACnB;AAED,MAAM,WAAW,mBAAmB;IAClC,gBAAgB,EAAE,mBAAmB,CAAC,eAAe,CAAC;IAEtD,OAAO,EAAE,MAAM,CAAC;IAEhB;;;OAGG;IACH,QAAQ,CAAC,EAAE,MAAM,CAAC;CACnB;AAED,yBAAiB,mBAAmB,CAAC;IACnC,UAAiB,eAAe;QAC9B;;WAEG;QACH,EAAE,CAAC,EAAE,MAAM,CAAC;QAEZ,MAAM,CAAC,EAAE,eAAe,CAAC,MAAM,CAAC;QAEhC;;WAEG;QACH,WAAW,CAAC,EAAE,MAAM,CAAC;QAErB,aAAa,CAAC,EAAE,MAAM,CAAC;KACxB;IAED,UAAiB,eAAe,CAAC;QAC/B,UAAiB,MAAM;YACrB,MAAM,CAAC,EAAE,KAAK,CAAC,MAAM,CAAC,CAAC;SACxB;KACF;CACF;AAED,MAAM,WAAW,qBAAqB;IACpC,OAAO,EAAE,MAAM,CAAC;IAEhB,OAAO,EAAE,MAAM,CAAC;CACjB;AAED,MAAM,WAAW,gBAAgB;IAC/B;;OAEG;IACH,EAAE,EAAE,MAAM,CAAC;IAEX;;OAEG;IACH,IAAI,CAAC,EAAE,MAAM,CAAC;CACf;AAED,MAAM,WAAW,gBAAgB;IAC/B;;;OAGG;IACH,YAAY,CAAC,EAAE,MAAM,CAAC;IAEtB;;;OAGG;IACH,MAAM,CAAC,EAAE,MAAM,EAAE,CAAC;CACnB;AAED,MAAM,CAAC,OAAO,WAAW,IAAI,CAAC;IAC5B,OAAO,EACL,KAAK,WAAW,IAAI,WAAW,EAC/B,KAAK,kBAAkB,IAAI,kBAAkB,EAC7C,KAAK,mBAAmB,IAAI,mBAAmB,EAC/C,KAAK,mBAAmB,IAAI,mBAAmB,EAC/C,KAAK,qBAAqB,IAAI,qBAAqB,EACnD,KAAK,gBAAgB,IAAI,gBAAgB,EACzC,KAAK,gBAAgB,IAAI,gBAAgB,GAC1C,CAAC;CACH"}