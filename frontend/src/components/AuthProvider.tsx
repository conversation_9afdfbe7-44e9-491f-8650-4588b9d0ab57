'use client';

import React, {
  createContext,
  useContext,
  useState,
  useEffect,
  ReactNode,
} from 'react';
import { createClient } from '@/lib/supabase/client';
import { User, Session } from '@supabase/supabase-js';
import { SupabaseClient } from '@supabase/supabase-js';
import { checkAndInstallSunaAgent } from '@/lib/utils/install-suna-agent';

type AuthContextType = {
  supabase: SupabaseClient;
  session: Session | null;
  user: User | null;
  isLoading: boolean;
  signOut: () => Promise<void>;
};

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const AuthProvider = ({ children }: { children: ReactNode }) => {
  const supabase = createClient();
  const [session, setSession] = useState<Session | null>(null);
  const [user, setUser] = useState<User | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    const getInitialSession = async () => {
      const {
        data: { session: currentSession },
      } = await supabase.auth.getSession();
      setSession(currentSession);
      setUser(currentSession?.user ?? null);
      setIsLoading(false);
    };

    getInitialSession();

    const { data: authListener } = supabase.auth.onAuthStateChange(
      async (event, newSession) => {
        console.log('🔵 Auth state change:', { event, session: !!newSession, user: !!newSession?.user });
        
        setSession(newSession);

        // Only update user state on actual auth events, not token refresh
        if (event === 'SIGNED_IN' || event === 'SIGNED_OUT') {
          setUser(newSession?.user ?? null);
        }
        // For TOKEN_REFRESHED events, keep the existing user state

        if (isLoading) setIsLoading(false);
        
        if (event === 'SIGNED_IN' && newSession?.user) {
          await checkAndInstallSunaAgent(newSession.user.id, newSession.user.created_at);
        } else if (event === 'MFA_CHALLENGE_VERIFIED') {
          console.log('✅ MFA challenge verified, session updated');
          // Session is automatically updated by Supabase, just log for debugging
        } else if (event === 'TOKEN_REFRESHED') {
          console.log('🔄 Token refreshed, session updated');
        }
      },
    );

    return () => {
      authListener?.subscription.unsubscribe();
    };
  }, [supabase, isLoading]); // Added isLoading to dependencies to ensure it runs once after initial load completes

  const signOut = async () => {
    await supabase.auth.signOut();
    // State updates will be handled by onAuthStateChange
  };

  const value = {
    supabase,
    session,
    user,
    isLoading,
    signOut,
  };

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
};

export const useAuth = (): AuthContextType => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};
