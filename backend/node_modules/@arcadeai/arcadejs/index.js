"use strict";
// File generated from our OpenAPI spec by Stainless. See CONTRIBUTING.md for details.
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
var _Arcade_instances, _a, _Arcade_baseURLOverridden;
Object.defineProperty(exports, "__esModule", { value: true });
exports.UnprocessableEntityError = exports.PermissionDeniedError = exports.InternalServerError = exports.AuthenticationError = exports.BadRequestError = exports.RateLimitError = exports.ConflictError = exports.NotFoundError = exports.APIUserAbortError = exports.APIConnectionTimeoutError = exports.APIConnectionError = exports.APIError = exports.ArcadeError = exports.fileFromPath = exports.toFile = exports.Arcade = void 0;
const qs = __importStar(require("./internal/qs/index.js"));
const Core = __importStar(require("./core.js"));
const Errors = __importStar(require("./error.js"));
const Pagination = __importStar(require("./pagination.js"));
const Uploads = __importStar(require("./uploads.js"));
const API = __importStar(require("./resources/index.js"));
const auth_1 = require("./resources/auth.js");
const health_1 = require("./resources/health.js");
const workers_1 = require("./resources/workers.js");
const admin_1 = require("./resources/admin/admin.js");
const chat_1 = require("./resources/chat/chat.js");
const tools_1 = require("./resources/tools/tools.js");
/**
 * API Client for interfacing with the Arcade API.
 */
class Arcade extends Core.APIClient {
    /**
     * API Client for interfacing with the Arcade API.
     *
     * @param {string | undefined} [opts.apiKey=process.env['ARCADE_API_KEY'] ?? undefined]
     * @param {string} [opts.baseURL=process.env['ARCADE_BASE_URL'] ?? https://api.arcade.dev] - Override the default base URL for the API.
     * @param {number} [opts.timeout=1 minute] - The maximum amount of time (in milliseconds) the client will wait for a response before timing out.
     * @param {number} [opts.httpAgent] - An HTTP agent used to manage HTTP(s) connections.
     * @param {Core.Fetch} [opts.fetch] - Specify a custom `fetch` function implementation.
     * @param {number} [opts.maxRetries=2] - The maximum number of times the client will retry a request.
     * @param {Core.Headers} opts.defaultHeaders - Default headers to include with every request to the API.
     * @param {Core.DefaultQuery} opts.defaultQuery - Default query parameters to include with every request to the API.
     */
    constructor({ baseURL = Core.readEnv('ARCADE_BASE_URL'), apiKey = Core.readEnv('ARCADE_API_KEY'), ...opts } = {}) {
        if (apiKey === undefined) {
            throw new Errors.ArcadeError("The ARCADE_API_KEY environment variable is missing or empty; either provide it, or instantiate the Arcade client with an apiKey option, like new Arcade({ apiKey: 'My API Key' }).");
        }
        const options = {
            apiKey,
            ...opts,
            baseURL: baseURL || `https://api.arcade.dev`,
        };
        super({
            baseURL: options.baseURL,
            baseURLOverridden: baseURL ? baseURL !== 'https://api.arcade.dev' : false,
            timeout: options.timeout ?? 60000 /* 1 minute */,
            httpAgent: options.httpAgent,
            maxRetries: options.maxRetries,
            fetch: options.fetch,
        });
        _Arcade_instances.add(this);
        this.admin = new API.Admin(this);
        this.auth = new API.Auth(this);
        this.health = new API.Health(this);
        this.chat = new API.Chat(this);
        this.tools = new API.Tools(this);
        this.workers = new API.Workers(this);
        this._options = options;
        this.idempotencyHeader = 'Idempotency-Key';
        this.apiKey = apiKey;
    }
    defaultQuery() {
        return this._options.defaultQuery;
    }
    defaultHeaders(opts) {
        return {
            ...super.defaultHeaders(opts),
            ...this._options.defaultHeaders,
        };
    }
    authHeaders(opts) {
        return { Authorization: this.apiKey };
    }
    stringifyQuery(query) {
        return qs.stringify(query, { arrayFormat: 'comma' });
    }
}
exports.Arcade = Arcade;
_a = Arcade, _Arcade_instances = new WeakSet(), _Arcade_baseURLOverridden = function _Arcade_baseURLOverridden() {
    return this.baseURL !== 'https://api.arcade.dev';
};
Arcade.Arcade = _a;
Arcade.DEFAULT_TIMEOUT = 60000; // 1 minute
Arcade.ArcadeError = Errors.ArcadeError;
Arcade.APIError = Errors.APIError;
Arcade.APIConnectionError = Errors.APIConnectionError;
Arcade.APIConnectionTimeoutError = Errors.APIConnectionTimeoutError;
Arcade.APIUserAbortError = Errors.APIUserAbortError;
Arcade.NotFoundError = Errors.NotFoundError;
Arcade.ConflictError = Errors.ConflictError;
Arcade.RateLimitError = Errors.RateLimitError;
Arcade.BadRequestError = Errors.BadRequestError;
Arcade.AuthenticationError = Errors.AuthenticationError;
Arcade.InternalServerError = Errors.InternalServerError;
Arcade.PermissionDeniedError = Errors.PermissionDeniedError;
Arcade.UnprocessableEntityError = Errors.UnprocessableEntityError;
Arcade.toFile = Uploads.toFile;
Arcade.fileFromPath = Uploads.fileFromPath;
Arcade.Admin = admin_1.Admin;
Arcade.Auth = auth_1.Auth;
Arcade.Health = health_1.Health;
Arcade.Chat = chat_1.Chat;
Arcade.Tools = tools_1.Tools;
Arcade.ToolDefinitionsOffsetPage = tools_1.ToolDefinitionsOffsetPage;
Arcade.Workers = workers_1.Workers;
Arcade.WorkerResponsesOffsetPage = workers_1.WorkerResponsesOffsetPage;
var uploads_1 = require("./uploads.js");
Object.defineProperty(exports, "toFile", { enumerable: true, get: function () { return uploads_1.toFile; } });
Object.defineProperty(exports, "fileFromPath", { enumerable: true, get: function () { return uploads_1.fileFromPath; } });
var error_1 = require("./error.js");
Object.defineProperty(exports, "ArcadeError", { enumerable: true, get: function () { return error_1.ArcadeError; } });
Object.defineProperty(exports, "APIError", { enumerable: true, get: function () { return error_1.APIError; } });
Object.defineProperty(exports, "APIConnectionError", { enumerable: true, get: function () { return error_1.APIConnectionError; } });
Object.defineProperty(exports, "APIConnectionTimeoutError", { enumerable: true, get: function () { return error_1.APIConnectionTimeoutError; } });
Object.defineProperty(exports, "APIUserAbortError", { enumerable: true, get: function () { return error_1.APIUserAbortError; } });
Object.defineProperty(exports, "NotFoundError", { enumerable: true, get: function () { return error_1.NotFoundError; } });
Object.defineProperty(exports, "ConflictError", { enumerable: true, get: function () { return error_1.ConflictError; } });
Object.defineProperty(exports, "RateLimitError", { enumerable: true, get: function () { return error_1.RateLimitError; } });
Object.defineProperty(exports, "BadRequestError", { enumerable: true, get: function () { return error_1.BadRequestError; } });
Object.defineProperty(exports, "AuthenticationError", { enumerable: true, get: function () { return error_1.AuthenticationError; } });
Object.defineProperty(exports, "InternalServerError", { enumerable: true, get: function () { return error_1.InternalServerError; } });
Object.defineProperty(exports, "PermissionDeniedError", { enumerable: true, get: function () { return error_1.PermissionDeniedError; } });
Object.defineProperty(exports, "UnprocessableEntityError", { enumerable: true, get: function () { return error_1.UnprocessableEntityError; } });
exports = module.exports = Arcade;
exports.default = Arcade;
//# sourceMappingURL=index.js.map