"use strict";
// File generated from our OpenAPI spec by Stainless. See CONTRIBUTING.md for details.
Object.defineProperty(exports, "__esModule", { value: true });
exports.ToolExecutionsOffsetPage = exports.Scheduled = void 0;
const resource_1 = require("../../resource.js");
const core_1 = require("../../core.js");
const tools_1 = require("./tools.js");
Object.defineProperty(exports, "ToolExecutionsOffsetPage", { enumerable: true, get: function () { return tools_1.ToolExecutionsOffsetPage; } });
class Scheduled extends resource_1.APIResource {
    list(query = {}, options) {
        if ((0, core_1.isRequestOptions)(query)) {
            return this.list({}, query);
        }
        return this._client.getAPIList('/v1/scheduled_tools', tools_1.ToolExecutionsOffsetPage, { query, ...options });
    }
    /**
     * Returns the details for a specific scheduled tool execution
     */
    get(id, options) {
        return this._client.get(`/v1/scheduled_tools/${id}`, options);
    }
}
exports.Scheduled = Scheduled;
//# sourceMappingURL=scheduled.js.map