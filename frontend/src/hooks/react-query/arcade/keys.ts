/**
 * Clés de cache React Query pour Arcade.dev
 * Compatible avec la structure Pipedream existante
 */

export const arcadeKeys = {
  // Clé racine pour toutes les requêtes Arcade
  all: ['arcade'] as const,
  
  // Connexions utilisateur
  connections: () => [...arcadeKeys.all, 'connections'] as const,
  
  // Toolkits (équivalent des apps Pipedream)
  toolkits: (search?: string, category?: string) => 
    [...arcadeKeys.all, 'toolkits', { search, category }] as const,
  
  popularToolkits: () => [...arcadeKeys.all, 'toolkits', 'popular'] as const,
  
  toolkit: (name: string) => [...arcadeKeys.all, 'toolkit', name] as const,
  
  // Découverte d'outils (équivalent MCP discovery)
  toolDiscovery: (options: { toolkit_name?: string; oauth_app_id?: string }) =>
    [...arcadeKeys.all, 'tools', 'discovery', options] as const,
  
  // Outils disponibles
  availableTools: () => [...arcadeKeys.all, 'tools', 'available'] as const,
  
  // Profils utilisateur
  profiles: () => [...arcadeKeys.all, 'profiles'] as const,
  
  profile: (profileId: string) => [...arcadeKeys.all, 'profile', profileId] as const,
  
  profilesByToolkit: (toolkit: string) => 
    [...arcadeKeys.all, 'profiles', 'by-toolkit', toolkit] as const,
  
  // Authentification
  auth: () => [...arcadeKeys.all, 'auth'] as const,
  
  authStatus: (authId: string) => [...arcadeKeys.all, 'auth', 'status', authId] as const,
  
  // Health check
  health: () => [...arcadeKeys.all, 'health'] as const,
} as const;

// Export des types pour TypeScript
export type ArcadeKeys = typeof arcadeKeys;
