import { APIResource } from "../resource.js";
import * as Core from "../core.js";
import * as ToolsAPI from "./tools/tools.js";
import { ToolDefinitionsOffsetPage } from "./tools/tools.js";
import { OffsetPage, type OffsetPageParams } from "../pagination.js";
export declare class Workers extends APIResource {
    /**
     * Create a worker
     */
    create(body: WorkerCreateParams, options?: Core.RequestOptions): Core.APIPromise<WorkerResponse>;
    /**
     * Update a worker
     */
    update(id: string, body: WorkerUpdateParams, options?: Core.RequestOptions): Core.APIPromise<WorkerResponse>;
    /**
     * List all workers with their definitions
     */
    list(query?: WorkerListParams, options?: Core.RequestOptions): Core.PagePromise<WorkerResponsesOffsetPage, WorkerResponse>;
    list(options?: Core.RequestOptions): Core.PagePromise<WorkerResponsesOffsetPage, WorkerResponse>;
    /**
     * Delete a worker
     */
    delete(id: string, options?: Core.RequestOptions): Core.APIPromise<void>;
    /**
     * Get a worker by ID
     */
    get(id: string, options?: Core.RequestOptions): Core.APIPromise<WorkerResponse>;
    /**
     * Get the health of a worker
     */
    health(id: string, options?: Core.RequestOptions): Core.APIPromise<WorkerHealthResponse>;
    /**
     * Returns a page of tools
     */
    tools(id: string, query?: WorkerToolsParams, options?: Core.RequestOptions): Core.PagePromise<ToolDefinitionsOffsetPage, ToolsAPI.ToolDefinition>;
    tools(id: string, options?: Core.RequestOptions): Core.PagePromise<ToolDefinitionsOffsetPage, ToolsAPI.ToolDefinition>;
}
export declare class WorkerResponsesOffsetPage extends OffsetPage<WorkerResponse> {
}
export interface CreateWorkerRequest {
    id: string;
    enabled?: boolean;
    http?: CreateWorkerRequest.HTTP;
    mcp?: CreateWorkerRequest.Mcp;
    type?: string;
}
export declare namespace CreateWorkerRequest {
    interface HTTP {
        retry: number;
        secret: string;
        timeout: number;
        uri: string;
    }
    interface Mcp {
        retry: number;
        timeout: number;
        uri: string;
    }
}
export interface UpdateWorkerRequest {
    enabled?: boolean;
    http?: UpdateWorkerRequest.HTTP;
    mcp?: UpdateWorkerRequest.Mcp;
}
export declare namespace UpdateWorkerRequest {
    interface HTTP {
        retry?: number;
        secret?: string;
        timeout?: number;
        uri?: string;
    }
    interface Mcp {
        retry?: number;
        timeout?: number;
        uri?: string;
    }
}
export interface WorkerHealthResponse {
    id?: string;
    enabled?: boolean;
    healthy?: boolean;
    message?: string;
}
export interface WorkerResponse {
    id?: string;
    binding?: WorkerResponse.Binding;
    enabled?: boolean;
    http?: WorkerResponse.HTTP;
    mcp?: WorkerResponse.Mcp;
    oxp?: WorkerResponse.Oxp;
    type?: 'http' | 'mcp' | 'unknown';
}
export declare namespace WorkerResponse {
    interface Binding {
        id?: string;
        type?: 'static' | 'tenant' | 'project' | 'account';
    }
    interface HTTP {
        retry?: number;
        secret?: HTTP.Secret;
        timeout?: number;
        uri?: string;
    }
    namespace HTTP {
        interface Secret {
            binding?: 'static' | 'tenant' | 'project' | 'account';
            editable?: boolean;
            exists?: boolean;
            hint?: string;
            value?: string;
        }
    }
    interface Mcp {
        retry?: number;
        timeout?: number;
        uri?: string;
    }
    interface Oxp {
        retry?: number;
        secret?: Oxp.Secret;
        timeout?: number;
        uri?: string;
    }
    namespace Oxp {
        interface Secret {
            binding?: 'static' | 'tenant' | 'project' | 'account';
            editable?: boolean;
            exists?: boolean;
            hint?: string;
            value?: string;
        }
    }
}
export interface WorkerCreateParams {
    id: string;
    enabled?: boolean;
    http?: WorkerCreateParams.HTTP;
    mcp?: WorkerCreateParams.Mcp;
    type?: string;
}
export declare namespace WorkerCreateParams {
    interface HTTP {
        retry: number;
        secret: string;
        timeout: number;
        uri: string;
    }
    interface Mcp {
        retry: number;
        timeout: number;
        uri: string;
    }
}
export interface WorkerUpdateParams {
    enabled?: boolean;
    http?: WorkerUpdateParams.HTTP;
    mcp?: WorkerUpdateParams.Mcp;
}
export declare namespace WorkerUpdateParams {
    interface HTTP {
        retry?: number;
        secret?: string;
        timeout?: number;
        uri?: string;
    }
    interface Mcp {
        retry?: number;
        timeout?: number;
        uri?: string;
    }
}
export interface WorkerListParams extends OffsetPageParams {
}
export interface WorkerToolsParams extends OffsetPageParams {
}
export declare namespace Workers {
    export { type CreateWorkerRequest as CreateWorkerRequest, type UpdateWorkerRequest as UpdateWorkerRequest, type WorkerHealthResponse as WorkerHealthResponse, type WorkerResponse as WorkerResponse, WorkerResponsesOffsetPage as WorkerResponsesOffsetPage, type WorkerCreateParams as WorkerCreateParams, type WorkerUpdateParams as WorkerUpdateParams, type WorkerListParams as WorkerListParams, type WorkerToolsParams as WorkerToolsParams, };
}
export { ToolDefinitionsOffsetPage };
//# sourceMappingURL=workers.d.ts.map